import calendar
import operator
from datetime import date, datetime
from functools import partial, reduce
from typing import Optional

from dateutil.relativedelta import relativedelta
from django.core.exceptions import ObjectDoesNotExist
from django.db.models import Q
from django.utils import timezone

from adm.models import (
    AdminGroup,
    Area,
    AreaHolidayApiMapping,
    ProviderProfile,
    Team,
)
from leave import (
    BALANCE_TYPE,
    BALANCE_TYPE_P,
    CARRYOVER_KEY,
    CARRYOVER_KEY_B,
    CARRYOVER_KEY_P,
    LEAVE_POLICY_FROM_ONBOARDING,
)
from leave.models import (
    CountryLeavePolicy,
    LeaveCountryPolicyAnnualCycle,
    LeavePolicy,
    LeaveType,
    LeaveTypePolicyRef,
    PartnerLeave,
    UserApplyLeave,
    UserCarryOverApply,
)
from leave.nodes.applyleave import CalculateLeaveDaysNode
from leave.nodes.carryoverleave import CarryoverDaysNode, HasCarryoverNode
from leave.nodes.partnerleave import (
    FilterPartnerCountryFromLeaveListNode,
    FilterPartnerEmployeeFromLeaveListNode,
    PartnerLeaveListNode,
)
from leave.utils import (
    LEAVE_KEY_MAP,
    TCP_KEYS_B,
    TCP_KEYS_N,
    TCP_KEYS_P,
    check_start_date,
    do_approval_permission,
    get_carryover,
    get_carryover_period_cond,
    get_country_code_for_workdays,
    get_teams,
    get_user_province_code,
    get_workdays_with_fallback,
)
from user.models import User, UserSetting
from utils.constant import (
    COUNTRY_LEVEL,
    HMT_CITIES,
    PEO_TYPE,
    STANDARD_COMPANY_TYPE,
)
from utils.constant import Permission
from utils.constant import Permission as PERMISSION
from utils.cryptography import filter_encrypt_users
from utils.djangoplus import holiday
from utils.exceptionhandler import ErrMsg
from utils.permission import permissions_checker

# For client leave
from utils.utils import divide_pages, get_logger, send_mail

LEAVE_REQUEST = 1
CARRYOVER_REQUEST = 2


def _get_user_annual_cycle_labels(usersetting, year, year_pointer):
    """
    Determines the correct annual cycle labels for a given user setting and year.
    Adjusts the year based on the reset month.
    """
    cycle_labels = {
        "current": None,
        "next": None,
        "last": None
    }

    annual_cycle = _get_user_annual_cycle(usersetting)
    if not annual_cycle:
        get_logger().info("resolve_leave_balance_list: annual_cycle is not found")
        return cycle_labels  # No cycle data, return empty

    if year_pointer == 0:
        cycle_labels["current"] = _build_user_annual_cycle_label(usersetting, TCP_KEYS_N, year)
        cycle_labels["last"] = _build_user_annual_cycle_label(usersetting, TCP_KEYS_P, year - 1)
        cycle_labels["next"] = _build_user_annual_cycle_label(usersetting, TCP_KEYS_B, year + 1)
    elif year_pointer == - 1:
        cycle_labels["current"] = _build_user_annual_cycle_label(usersetting, TCP_KEYS_P, year)
        cycle_labels["last"] = None
        cycle_labels["next"] = _build_user_annual_cycle_label(usersetting, TCP_KEYS_N, year + 1)
    elif year_pointer == 1:
        cycle_labels["current"] = _build_user_annual_cycle_label(usersetting, TCP_KEYS_B, year)
        cycle_labels["next"] = None
        cycle_labels["last"] = _build_user_annual_cycle_label(usersetting, TCP_KEYS_N, year - 1)

    return cycle_labels


def _build_user_annual_cycle_label(user_setting, balance_key, requested_year) -> Optional[str]:
    """
    Builds and returns a label representing the user's annual leave cycle range.
    """
    user = user_setting.user
    annual_cycle = _get_user_annual_cycle(user_setting)

    if not annual_cycle:
        get_logger().info("resolve_leave_balance_list: annual_cycle is not found")
        return None

        # Determine cycle title based on balance_key list
    if balance_key == TCP_KEYS_N:
        cycle_title = "Current Cycle"
    elif balance_key == TCP_KEYS_B:
        cycle_title = "Next Cycle"
    elif balance_key == TCP_KEYS_P:
        cycle_title = "Last Cycle"
    else:
        get_logger().info(f"resolve_leave_balance_list: Unknown balance key list: {balance_key}")
        return None

    if not cycle_title:
        get_logger().info(f"resolve_leave_balance_list: Unknown balance key list: {balance_key}")
        return None

    if annual_cycle.reset_method == LeaveCountryPolicyAnnualCycle.RESET_ON_START_DATE:
        return _handle_reset_on_start_date(user, cycle_title, requested_year)

    elif annual_cycle.reset_method == LeaveCountryPolicyAnnualCycle.RESET_YEARLY:
        return _handle_reset_yearly(annual_cycle, cycle_title, requested_year)

    else:
        get_logger().warn(f"resolve_leave_balance_list: Unknown reset method: {annual_cycle.reset_method}")
        return None


def _handle_reset_on_start_date(user, cycle_title, requested_year) -> Optional[str]:
    """
    Handles the case where the annual cycle resets on the user's start date.
    Ensures cycle ranges are correctly calculated.
    """
    user_start_date = getattr(user, "start_date", None)
    if not user_start_date:
        get_logger().warn("resolve_leave_balance_list: User has no start_date. Cannot determine cycle.")
        return None

    return _format_cycle_label(cycle_title, user_start_date.month, requested_year)


def _handle_reset_yearly(annual_cycle, cycle_title, requested_year) -> Optional[str]:
    """
    Handles the case where the annual cycle resets on a specific month each year.
    Ensures that cycle ranges are properly calculated.
    """
    reset_month = getattr(annual_cycle, "reset_month", None)
    if reset_month is None:
        get_logger().warn(
            f"Leave Balance List: Annual cycle for country {annual_cycle.country.id} has no reset_month configured."
        )
        return None

    return _format_cycle_label(cycle_title, reset_month, requested_year)


def _format_cycle_label(cycle_title: str, reset_month: int, requested_year: int) -> str:
    """
    Formats the cycle label based on the reset month and requested year.

    Args:
        cycle_title: "Current Cycle", "Next Cycle", or "Last Cycle".
        reset_month: The month when the cycle resets (e.g., April = 4, January = 1).
        requested_year: The year used to determine the cycle.

    Returns:
        str: Formatted cycle label (e.g., "Current Cycle: Apr 2024 - Mar 2025").
    """
    current_month = timezone.now().month  # Get the current month

    # Determine the correct start year based on reset month and current month
    if current_month < reset_month:
        cycle_start_year = requested_year - 1  # If we're before the reset month, the cycle started last year
    else:
        cycle_start_year = requested_year  # Otherwise, it started this year

    start_month = calendar.month_abbr[reset_month]  # Convert reset month to abbreviation.env
    end_month = calendar.month_abbr[(reset_month - 2) % 12 + 1]  # Month before reset month

    # If reset is in January, the cycle ends the same year; otherwise, it ends the next year
    cycle_end_year = cycle_start_year if reset_month == 1 else cycle_start_year + 1

    return f"{cycle_title}: {start_month} {cycle_start_year} - {end_month} {cycle_end_year}"


def _get_user_annual_cycle(user_setting):
    """
    Get user's annual cycle.
    """
    user = user_setting.user
    country = getattr(user, "location_country", None)
    if not country:
        get_logger().warn(
            f"Leave Balance List: User {user.id} does not have a location_country associated."
        )
        return None

    # Fetch the user's annual cycle
    user_annual_cycle = LeaveCountryPolicyAnnualCycle.objects.filter(
        country=country
    ).first()
    if not user_annual_cycle:
        get_logger().warn(
            f"Leave Balance List: No annual cycle found for country: {country}"
        )
        return None

    return user_annual_cycle


def is_valid_carryover_month(user_setting, year):
    """
    Determines if the carryover option should be shown based on the user's annual cycle and the specified year.
    """

    user = user_setting.user
    country = getattr(user, "location_country", None)
    if not country:
        get_logger().warn(
            f"Leave Balance List: User {user.id} does not have a location_country associated."
        )
        return False

    # Fetch the user's annual cycle
    user_annual_cycle = LeaveCountryPolicyAnnualCycle.objects.filter(
        country=country
    ).first()
    if not user_annual_cycle:
        get_logger().warn(
            f"Leave Balance List: No annual cycle found for country: {country}"
        )
        return False

    if not year:
        get_logger().warn("Leave Balance List: Year has no value.")
        return False

    current_date = timezone.now()
    current_month = current_date.month
    current_year = current_date.year

    if (
        user_annual_cycle.reset_method
        == LeaveCountryPolicyAnnualCycle.RESET_ON_START_DATE
    ):
        user_start_date = getattr(user, "start_date", None)
        if not user_start_date:
            get_logger().warn(
                f"Leave Balance List: User {user.id} does not have a start_date."
            )
            return False

        start_month = user_start_date.month

        # Check visibility for the specified year
        if year == current_year and current_month in {
            (start_month - 1) % 12 or 12,
            start_month,
        }:
            return True

    elif (
        user_annual_cycle.reset_method
        == LeaveCountryPolicyAnnualCycle.RESET_YEARLY
    ):
        reset_month = getattr(user_annual_cycle, "reset_month", None)
        if reset_month is None:
            get_logger().warn(
                f"Leave Balance List: Annual cycle for country: {country} doesn't have a configured reset_month."
            )
            return False

        # Check visibility for the specified year
        if year == current_year and current_month in {
            (reset_month - 1) % 12 or 12,
            reset_month,
        }:
            return True

    return False


def get_holidays_with_fallback(year, location, province_code, province_name):
    """
    Fetches holidays first using Country-Province format.
    If no results, falls back to Country-only.

    Returns:
        - List of holidays
        - The used location format (e.g., "Country-Province" or just "Country")
    """

    def fetch_holidays(country_code, subdivisions):
        """
        Helper function to fetch holidays from the API.
        Logs and handles API failures.
        """
        try:
            get_logger().info(f"[HOLIDAYS] Fetching holidays for {country_code}, Year: {year}")
            get_holiday_partial = partial(
                holiday.V1().get_holidays_of_year,
                country_code=country_code,
                subdivisions=subdivisions,
            )
            holidays = get_holiday_partial(year=year)

            if holidays and not (isinstance(holidays, dict) and "error" in holidays):
                return holidays
            return None  # Indicate failure
        except Exception as e:
            get_logger().error(f"[HOLIDAY] API request failed for {country_code}: {str(e)}")
            return None  # Indicate failure

    # Try fetching holidays with province first
    if province_code:
        country_province_code = f"{location.alpha2_code}-{province_code}"
        holidays = fetch_holidays(country_province_code, True)
        country_label = f"{province_name}({location.name_en})"

        if holidays:
            return holidays, country_label  # Successfully fetched with province

        # Log warning & retry with country-only
        get_logger().warning(f"[HOLIDAYS] No results for {country_province_code}. Retrying without province.")

    # Fallback: Try country-only
    country_code = location.alpha2_code
    holidays = fetch_holidays(country_code, False)
    country_label = location.name_en

    if holidays:
        return holidays, country_label  # Successfully fetched at country level

    # Both province & country fetch failed
    get_logger().error(f"[HOLIDAY]S Failed to fetch holidays for {year}. No results for {country_code}.")
    return [], country_code


def is_valid_carryover_policy(country_id, leave_type_id):
    country_leave_policy = CountryLeavePolicy.objects.filter(
        country_id=country_id, leave_type_id=leave_type_id
    ).first()
    return (
        True
        if country_leave_policy and country_leave_policy.carryover_enabled
        else False
    )


class ResolverMixin:
    @staticmethod
    def resolve_leave_policy(root, info, id):
        user = info.context.user
        try:
            return (
                LeavePolicy.objects.get(pk=id, provider_id=user.work_for)
                if id
                else user.usersetting.leave_policy
            )
        except LeavePolicy.DoesNotExist:
            return None

    @staticmethod
    @permissions_checker(
        permissions=[
            PERMISSION.LEAVE_INTERNAL_MYLEAVE,
            PERMISSION.CLIENT_LEAVE_MYLEAVE,
        ]
    )
    def resolve_my_apply_leave(root, info, id):
        try:
            return UserApplyLeave.objects.get(user=info.context.user, id=id)
        except UserApplyLeave.DoesNotExist:
            return None

    @staticmethod
    @permissions_checker(
        permissions=[
            PERMISSION.LEAVE_INTERNAL_APPROVAL,
            PERMISSION.CLIENT_LEAVE_APPROVAL,
        ]
    )
    def resolve_leave_approval(root, info, id):
        try:
            return UserApplyLeave.objects.get(id=id)
        except UserApplyLeave.DoesNotExist:
            return None

    @staticmethod
    def resolve_applied_user_list(
        root, info, work_for, work_for_type, model_field, model_field_id
    ):
        try:
            model_field = {
                "leaveProcess": "leave_process_id",
                "leavePolicy": "leave_policy_id",
            }[model_field]
            if "leave_policy_id" == model_field:
                q = Q(**{f"usersetting__{model_field}__isnull": True})
                if model_field_id:
                    q |= Q(**{f"usersetting__{model_field}": model_field_id})
            else:
                q = Q()
        except KeyError:
            return ErrMsg.REQERRMSG
        if STANDARD_COMPANY_TYPE == work_for_type:
            user = info.context.user
            return User.objects.filter(
                work_for=work_for,
                work_for_type=work_for_type,
                phase=User.ACTIVE_PHASE,
                employer_of_record=user.work_for,
                employer_of_record_type=user.work_for_type,
                user_type=User.CORE_USER,
            ).filter(q)
        elif PEO_TYPE == work_for_type:
            return User.objects.filter(
                work_for=work_for,
                phase=User.ACTIVE_PHASE,
                work_for_type=work_for_type,
                user_type=User.CORE_USER,
            ).filter(q)

    @staticmethod
    def resolve_assignee_group_list(root, info, work_for, work_for_type):
        if STANDARD_COMPANY_TYPE == work_for_type:
            user = info.context.user
            return AdminGroup.objects.filter(
                team_id=work_for,
                creator__work_for=user.work_for,
                creator__work_for_type=user.work_for_type,
                local_entity__isnull=True,
            ).order_by("name")
        elif PEO_TYPE == work_for_type:
            return AdminGroup.objects.filter(
                provider_id=work_for, team__isnull=True
            ).order_by("name")

    @staticmethod
    def resolve_assignee_user_list(root, info, work_for, work_for_type):
        user = info.context.user
        return User.objects.filter(
            phase=User.ACTIVE_PHASE,
            work_for=work_for,
            work_for_type=work_for_type,
        ).filter(
            Q(
                employer_of_record_type=user.work_for_type,
                employer_of_record=user.work_for,
            )
            | Q(employer_of_record__isnull=True)
        )

    @staticmethod
    @permissions_checker(permissions=[PERMISSION.LEAVE_CLIENT_VIEW])
    def resolve_leave_client_list(root, info, field):
        if field == LEAVE_REQUEST:
            condition = Q(
                users__apply_leaves__isnull=False,
                users__user_type=User.CORE_USER,
            )
        elif field == CARRYOVER_REQUEST:
            condition = Q(
                users__apply_carryovers__isnull=False,
                users__user_type=User.CORE_USER,
            )
        else:
            return None
        teams = get_teams(info.context.user, condition, ("id", "name"))
        return sorted(teams, key=lambda x: x.name)

    @staticmethod
    @permissions_checker(
        permissions=[
            PERMISSION.LEAVE_INTERNAL_MYLEAVE,
            PERMISSION.ADMIN_CLIENTUSER_VIEW,
            PERMISSION.USER_TEAM_EMPLOYEES_PROFILE,
            PERMISSION.CLIENT_LEAVE_MYLEAVE,
            PERMISSION.ADMIN_INTERNALUSER_VIEW,
        ]
    )
    def resolve_leave_balance_list(
        root, info, user_id, include_indefinite, year, pagable, page, per_page
    ):
        if not user_id:
            usersetting = info.context.user.usersetting
        else:
            usersetting = UserSetting.objects.filter(user_id=user_id).first()
        if not usersetting or not usersetting.leave_policy:
            return
        leave_policy = LeavePolicy.objects.filter(
            id=usersetting.leave_policy.id
        ).first()

        now_year = timezone.now().year
        current = True
        year_pointer = 0  # 0: current year, 1: next year, -1: previous year
        if not year or year == now_year:
            balance_key = TCP_KEYS_N
            carryover_key = CARRYOVER_KEY
            year_pointer = 0
        elif year == now_year + 1:
            balance_key = TCP_KEYS_B
            carryover_key = CARRYOVER_KEY_B
            current = False
            year_pointer = 1
        elif year == now_year - 1:
            balance_key = TCP_KEYS_P
            carryover_key = CARRYOVER_KEY_P
            current = False
            year_pointer = -1
        else:
            return

        if leave_policy.name != LEAVE_POLICY_FROM_ONBOARDING:
            return ResolverMixin._resolve_leave_balance_list(
                root,
                info,
                user_id,
                include_indefinite,
                year,
                pagable,
                page,
                per_page,
                balance_key,
                carryover_key,
                current,
                year_pointer
            )
        else:
            # from onboarding
            balance = usersetting.balance
            result = []
            show_carryover = is_valid_carryover_month(usersetting, year)
            cycle_labels = _get_user_annual_cycle_labels(usersetting, year, year_pointer)

            for b in balance:
                if not include_indefinite:
                    if b.get("has_indefinite"):
                        continue
                leave_type = LeaveType.objects.filter(id=b.get("id")).first()
                if year_pointer == 0:
                    available = round(float(b.get("available", 0)), 2)
                elif year_pointer == 1:
                    available = round(float(b.get("future_available", 0)), 2)
                else:
                    available = round(float(b.get("last_total", 0)), 2)
                result.append(
                    dict(
                        color=leave_type.color,
                        name=leave_type.name,
                        has_indefinite=b.get("has_indefinite"),
                        available=available,
                        id=b.get("id"),
                        has_carryover=is_valid_carryover_policy(
                            usersetting.user.location_country.id, leave_type.id
                        ),
                        show_carryover=show_carryover,
                        pending=round(float(b.get(balance_key[-1], 0)), 2),
                        carryover=round(float(b.get(carryover_key, 0)), 2),
                        consumed=round(float(b.get(balance_key[1], 0)), 2),
                    )
                )
            return dict(results=result,
                        total_count=len(result),
                        current_annual_cycle=cycle_labels["current"],
                        next_annual_cycle=cycle_labels["next"],
                        last_annual_cycle=cycle_labels["last"]
                        )

    @classmethod
    def _resolve_leave_balance_list(
        cls,
        root,
        info,
        user_id,
        include_indefinite,
        year,
        pagable,
        page,
        per_page,
        balance_key,
        carryover_key,
        current,
        year_pointer
    ):
        try:
            if not user_id:
                usersetting = info.context.user.usersetting
            else:
                usersetting = UserSetting.objects.get(user_id=user_id)
            if year and year < usersetting.user.start_date.year:
                return
            balances = usersetting.balance or []
            show_carryover = is_valid_carryover_month(usersetting, year)
            cycle_labels = _get_user_annual_cycle_labels(usersetting, year, year_pointer)
            leave_policy, has_updated_policy = (
                usersetting.leave_policy,
                usersetting.has_updated_policy,
            )
            leave_policy_name = leave_policy.name
            res = []
            leave_types = LeaveType.objects.prefetch_related(
                "leavetypepolicyref_set"
            ).in_bulk(
                [
                    balance["id"]
                    for balance in balances
                    if not balance.get("has_indefinite")
                ]
                if not include_indefinite
                else [balance["id"] for balance in balances]
            )
            for balance in balances:
                try:
                    lt = leave_types[balance["id"]]
                except KeyError:
                    continue
                has_carryover, balance_type = False, balance.get(
                    "type", BALANCE_TYPE
                )
                if TCP_KEYS_P != balance_key:
                    if BALANCE_TYPE_P == balance_type:
                        continue
                else:
                    if (
                        has_updated_policy and BALANCE_TYPE == balance_type
                    ) or not balance.get("has_carryover", True):
                        continue
                if current:
                    lt_tmp = lt.leavetypepolicyref_set.get(
                        leave_policy=leave_policy
                    )
                    if not has_updated_policy:
                        has_carryover = balance.get(
                            "has_carryover", lt_tmp.has_carryover
                        )
                    available = balance.get(balance_key[0], lt_tmp.entitlement)
                else:
                    available = balance.get(
                        balance_key[0],
                        lt.leavetypepolicyref_set.get(
                            leave_policy=leave_policy
                        ).entitlement
                        if TCP_KEYS_B == balance_key
                        else balance["available"]
                        + balance.get("consumed", 0)
                        + balance.get("pending", 0),
                    )
                available = available if available else 0
                # calculate has_carryover
                leave_ref = LeaveTypePolicyRef.objects.filter(
                    leave_policy_id=leave_policy.id,
                    leave_type_id=lt.id,
                ).first()
                if leave_ref and leave_ref.has_carryover:
                    has_carryover = True

                res.append(
                    dict(
                        color=lt.color,
                        name=lt.name,
                        has_indefinite=balance.get("has_indefinite"),
                        available=round(float(available), 2),
                        id=balance["id"],
                        has_carryover=has_carryover,
                        show_carryover=show_carryover,
                        pending=balance.get(balance_key[-1], 0),
                        carryover=balance.get(carryover_key, 0)
                        if has_carryover
                        else 0,
                        consumed=balance.get(balance_key[1], 0),
                    )
                )
            if (
                TCP_KEYS_P != balance_key
                and leave_policy_name != LEAVE_POLICY_FROM_ONBOARDING
            ):
                # TODO lazy fetch
                for ab in (
                    LeaveTypePolicyRef.objects.select_related("leave_type")
                    .filter(
                        leave_policy=leave_policy,
                        leave_type__has_indefinite=False,
                    )
                    .exclude(leave_type__in=leave_types)
                    .iterator()
                ):
                    leave_type = ab.leave_type or 0
                    res.append(
                        dict(
                            color=leave_type.color,
                            name=leave_type.name,
                            has_indefinite=balance.get("has_indefinite"),
                            available=round(float(ab.entitlement), 2),
                            consumed=0,
                            id=leave_type.id,
                            has_carryover=ab.has_carryover
                            if current
                            else False,
                            show_carryover=show_carryover,
                            carryover=0,
                            pending=0,
                        )
                    )

            result = sorted(res, key=lambda x: x["id"])
            if not per_page:
                per_page = 20
            if not page or page <= 0:
                page = 0
            else:
                page = page - 1

            if not pagable:
                return dict(results=result,
                            total_count=len(result),
                            current_annual_cycle=cycle_labels["current"],
                            next_annual_cycle=cycle_labels["next"],
                            last_annual_cycle=cycle_labels["last"])

            result_list = list(divide_pages(result, per_page))
            to_return = []
            if len(result_list) >= page + 1:
                to_return = result_list[page]
            return dict(results=to_return,
                        total_count=len(result),
                        current_annual_cycle=cycle_labels["current"],
                        next_annual_cycle=cycle_labels["next"],
                        last_annual_cycle=cycle_labels["last"]
                        )

        except UserSetting.DoesNotExist:
            return []
        except Exception:
            return None

    @staticmethod
    @permissions_checker(permissions=[PERMISSION.LEAVE_POLICY_FULLACCESS])
    def resolve_applied_client_list(root, info, leave_policy, include_self):
        user = info.context.user
        work_for, work_for_type = user.work_for, user.work_for_type
        q = Q(usersetting__leave_policy__isnull=True) | Q(
            usersetting__leave_policy_id=leave_policy
        )

        teams = get_teams(user, only_field=("id", "name"))
        core_users = User.objects.filter(user_type=User.CORE_USER)

        if teams:
            team_conditions = reduce(
                operator.or_,
                (
                    Q(work_for=team.id, work_for_type=STANDARD_COMPANY_TYPE)
                    for team in teams.iterator()
                ),
            )
            team_ids = (
                core_users.filter(
                    employer_of_record=work_for,
                    employer_of_record_type=work_for_type,
                )
                .filter(team_conditions)
                .filter(q)
                .values_list("work_for", flat=True)
            )
        else:
            team_ids = []
        # if peos:
        #     peo_conditions = reduce(
        #         operator.or_,
        #         (Q(work_for=peo.id, work_for_type=PEO_TYPE)
        #          for peo in peos.iterator())
        #     )
        #     peo_ids = core_users.filter(peo_conditions).filter(q).values_list('work_for', flat=True)
        # else:
        #     peo_ids = []
        # results = sorted(chain(teams.filter(pk__in=team_ids), peos.filter(pk__in=peo_ids)), key=lambda x: x.name)
        results = sorted(teams.filter(pk__in=team_ids), key=lambda x: x.name)
        if include_self is True:
            try:
                us = core_users.filter(
                    work_for=work_for, work_for_type=work_for_type
                ).filter(q)
                if (
                    STANDARD_COMPANY_TYPE == work_for_type
                    and us.filter(
                        employer_of_record=work_for,
                        employer_of_record_type=work_for_type,
                    ).exists()
                ):
                    results.insert(0, Team.objects.get(pk=work_for))
                elif us.exists():
                    results.insert(0, ProviderProfile.objects.get(pk=work_for))
            except ObjectDoesNotExist:
                ...
            except Exception:
                return

        for t_p in results:
            t_p.has_applying_leave = t_p.users.filter(
                apply_leaves__has_finish=False,
                usersetting__leave_policy_id=leave_policy,
            ).exists()
        return results

    @staticmethod
    @permissions_checker(
        permissions=[
            PERMISSION.LEAVE_INTERNAL_MYLEAVE,
            PERMISSION.CLIENT_LEAVE_MYLEAVE,
        ]
    )
    def resolve_calculate_leave_days(
            _, info, start_date, end_date, start_date_half, end_date_half
    ):
        get_logger().info(f"resolve_calculate_leave_days: start_date: {start_date}, end_date: {end_date}, start_date_half: {start_date_half}, end_date_half: {end_date_half}")
        half_day = UserApplyLeave.HALF_DATE
        assert (
            start_date_half in half_day
            or start_date_half == UserApplyLeave.HALF_ALL
        ), ErrMsg.REQERRMSG
        assert (
            end_date_half in half_day
            or end_date_half == UserApplyLeave.HALF_ALL
        ), ErrMsg.REQERRMSG
        user = info.context.user
        code, original_code = get_country_code_for_workdays(user)
        try:
            days = get_workdays_with_fallback(
                code,
                start_date,
                end_date,
                start_date_half,
                end_date_half,
                original_code=original_code
            )
            return CalculateLeaveDaysNode(leave_days=days)
        except Exception as e:
            get_logger().error(f"Get workdays failed for code: {code}: {e}. original_code: {original_code}")
            return CalculateLeaveDaysNode(leave_days=-1)

    @staticmethod
    @permissions_checker(
        permissions=[
            PERMISSION.LEAVE_INTERNAL_MYLEAVE,
            PERMISSION.CLIENT_LEAVE_MYLEAVE,
        ]
    )
    def resolve_leave_history_list(root, info):
        now = timezone.now()
        return UserApplyLeave.objects.filter(
            user=info.context.user,
            has_finish=True,
            has_rejected=False,
            start_date__lte=now,
            end_date__gte=date(now.year, 1, 1),
        ).order_by("-start_date")

    @staticmethod
    def get_carryover_days_from_onboarding(
        leave_type_id, balances, user, is_expiration_enable, month, day
    ):
        country_leave_policy = CountryLeavePolicy.objects.filter(
            country_id=user.location_country.id, leave_type_id=leave_type_id
        ).first()
        if country_leave_policy:
            if country_leave_policy.carryover_enabled:
                if (
                    country_leave_policy.carryover_days_type
                    == CountryLeavePolicy.CARRYOVER_MAXIMUM_DAYS
                ):
                    days = country_leave_policy.carryover_max_days
                    return CarryoverDaysNode(
                        days=round(float(days), 2),
                        is_expiration_enable=is_expiration_enable,
                        month=month,
                        day=day,
                    )

        days = 0
        for balance in balances:
            if balance.get("id") == leave_type_id:
                days = balance.get("available", 0)
                break
        return CarryoverDaysNode(
            days=round(float(days), 2),
            is_expiration_enable=is_expiration_enable,
            month=month,
            day=day,
        )

    @staticmethod
    def resolve_carryover_days(root, info, leave_type_id):
        user = info.context.user
        start_year = user.start_date.year
        usersetting = user.usersetting
        if not usersetting or not usersetting.leave_policy:
            return None
        # variables to return
        is_expiration_enable = False
        month = None
        day = None
        leave_country_policy_annual_cycle = (
            LeaveCountryPolicyAnnualCycle.objects.filter(
                country_id=user.location_country.id
            ).first()
        )
        now = date.today()
        if leave_country_policy_annual_cycle:
            if (
                leave_country_policy_annual_cycle.reset_method
                == LeaveCountryPolicyAnnualCycle.RESET_YEARLY
            ):
                is_expiration_enable = (
                    leave_country_policy_annual_cycle.expiration_enabled
                )
                if leave_country_policy_annual_cycle.expiration_enabled:
                    reset_month = leave_country_policy_annual_cycle.reset_month
                    reset_date = date(now.year, reset_month, 1)
                    one_day_before_expiration = (
                        reset_date
                        + relativedelta(
                            months=leave_country_policy_annual_cycle.expiration_months
                        )
                    ) - relativedelta(days=1)
                    day = one_day_before_expiration.day
                    month = one_day_before_expiration.month
            else:
                is_expiration_enable = (
                    leave_country_policy_annual_cycle.expiration_enabled
                )
                if leave_country_policy_annual_cycle.expiration_enabled:
                    reset_date = user.start_date
                    one_day_before_expiration = (
                        reset_date
                        + relativedelta(
                            months=leave_country_policy_annual_cycle.expiration_months
                        )
                    ) - relativedelta(days=1)
                    day = one_day_before_expiration.day
                    month = one_day_before_expiration.month

        if usersetting.leave_policy.name == LEAVE_POLICY_FROM_ONBOARDING:
            return ResolverMixin.get_carryover_days_from_onboarding(
                leave_type_id,
                usersetting.balance,
                user,
                is_expiration_enable,
                month,
                day,
            )
        try:
            leave_type = LeaveTypePolicyRef.objects.select_related(
                "leave_policy"
            ).get(
                leave_policy=usersetting.leave_policy,
                leave_type_id=leave_type_id,
                has_carryover=True,
            )
        except (ObjectDoesNotExist, AttributeError):
            return None
        now = timezone.now()
        carryover_temp = get_carryover(
            now.date(), leave_type.leave_policy.reset_date
        )
        if not check_start_date(start_year, now.year, carryover_temp):
            return CarryoverDaysNode(
                days=0,
                is_expiration_enable=is_expiration_enable,
                month=month,
                day=day,
            )
        tcp_keys = LEAVE_KEY_MAP[carryover_temp]
        has_limit = (
            LeaveTypePolicyRef.LIMIT_CARRYOVER == leave_type.carryover_type
        )
        balances = usersetting.balance
        try:
            balance = filter(
                lambda x: x["id"] == leave_type_id
                and BALANCE_TYPE_P != x.get("type"),
                balances,
            ).__next__()

            if balance.get("has_indefinite"):
                return CarryoverDaysNode(
                    days=0,
                    is_expiration_enable=is_expiration_enable,
                    month=month,
                    day=day,
                )

            # TODO default available in last year
            _days = days = balance.get(
                tcp_keys[0],
                balance["available"]
                + balance.get("consumed", 0)
                + balance.get("pending", 0),
            )
            if has_limit:
                _days = leave_type.carryover_days
                _days -= sum(
                    user.apply_carryovers.filter(
                        leave_type=leave_type, has_rejected=False
                    )
                    .filter(get_carryover_period_cond(carryover_temp))
                    .values_list("days", flat=True)
                )
            if _days <= 0:
                return CarryoverDaysNode(
                    days=0,
                    is_expiration_enable=is_expiration_enable,
                    month=month,
                    day=day,
                )
        except StopIteration:
            # _days = days = leave_type.entitlement
            # if has_limit:
            #     _days = leave_type.carryover_days
            return CarryoverDaysNode(
                days=0,
                is_expiration_enable=is_expiration_enable,
                month=month,
                day=day,
            )
        except KeyError:
            return None
        return CarryoverDaysNode(
            days=min(days, _days),
            is_expiration_enable=is_expiration_enable,
            month=month,
            day=day,
        )

    @staticmethod
    @permissions_checker(
        permissions=[
            PERMISSION.LEAVE_INTERNAL_APPROVAL,
            PERMISSION.CLIENT_LEAVE_APPROVAL,
        ]
    )
    def resolve_carryover_approval(root, info, id):
        try:
            return UserCarryOverApply.objects.get(id=id)
        except UserCarryOverApply.DoesNotExist:
            return None

    @staticmethod
    @permissions_checker(
        permissions=[
            PERMISSION.LEAVE_INTERNAL_APPROVAL,
            PERMISSION.CLIENT_LEAVE_APPROVAL,
        ]
    )
    def resolve_has_carryover(root, info):
        return HasCarryoverNode(
            status=UserCarryOverApply.objects.filter(
                do_approval_permission(info.context.user)
                | Q(approvers__contains=info.context.user.id),
                has_finish=False,
            ).exists()
        )

    @staticmethod
    @permissions_checker(permissions=[PERMISSION.LEAVE_CLIENT_VIEW])
    def resolve_has_client_carryover(root, info, date):
        return HasCarryoverNode(
            status=UserCarryOverApply.objects.filter(
                user__work_for__in=get_teams(info.context.user),
                user__work_for_type=User.STANDARD_COMPANY,
                created_at__year=date.year,
                created_at__month=date.month,
            ).exists()
        )

    @staticmethod
    def resolve_holiday_list(root, info, years):
        """
        Resolves the holiday list for the given years, handling retries for invalid province codes.
        """
        years = years.split(",")
        user = info.context.user

        # Max check for years or user type
        if len(years) > 3 or User.COLLABORATOR == user.user_type:
            return None

        location, location_city = user.location_country, user.location_city
        hs = list()  # Final holidays list

        try:
            # Handle special cases for Hongkong, Macao, Taiwan
            if location_city and location_city.id in HMT_CITIES:
                location = location_city

            # Get province details
            province_code, province_name, province_id = get_user_province_code(user.id)

            if province_id:
                # Fetch subdivision mapping if applicable
                subdivision_mapping = None
                try:
                    subdivision_mapping = AreaHolidayApiMapping.objects.filter(
                        area_id=province_id
                    ).first()
                except Exception as e:
                    get_logger().error(f"Mapping lookup error: {e}")

                if subdivision_mapping:
                    province_code = subdivision_mapping.subdivision_code

            # Process holidays for each year
            for y in years:
                holidays = []
                try:
                    # Fetch holidays and determine if the province was used
                    holidays_data, country = get_holidays_with_fallback(year=y, location=location,
                                                                        province_code=province_code,
                                                                        province_name=province_name)
                    for h in holidays_data:
                        holidays.append(
                            dict(
                                date=h["observed"],
                                name=h["name"],
                                country=country,
                            )
                        )

                    hs.append(
                        dict(
                            year=y,
                            holiday=holidays,
                        )
                    )
                except Exception as esp:
                    get_logger().warn(f"Error processing year {y}: {str(esp)}")
                    continue

        except Exception as e:
            get_logger().error(f"Unexpected error: {str(e)}")

        return hs

    @staticmethod
    @permissions_checker(
        permissions=[
            Permission.ADMIN_COUNTRY_FULLACCESS,
            Permission.ADMIN_COUNTRY_VIEW,
            Permission.ADMIN_COUNTRY_EDIT,
        ]
    )
    def resolve_country_leave_policy(root, info, id):
        try:
            return CountryLeavePolicy.objects.get(pk=id)
        except CountryLeavePolicy.DoesNotExist:
            return None

    @staticmethod
    @permissions_checker(permissions=[Permission.PARTNER_DOCUMENTS_FULLACCESS])
    def resolve_partner_leave_list(root, info, **kwargs):
        partner_id = kwargs.get("partner_id")
        page_number = kwargs.get("page", 1)
        page_size = kwargs.get("per_page", 10)
        pagable = kwargs.get("pagable", True)
        user_ids = kwargs.get("user_ids", "")
        country_ids = kwargs.get("country_ids", "")
        leave_month = kwargs.get("leave_month", "")

        leave_ids = PartnerLeave.objects.filter(
            partner_id=partner_id
        ).values_list("leave_id", flat=True)
        query = UserApplyLeave.objects_all.select_related(
            "user",
            "leave_type",
        ).filter(id__in=leave_ids)
        if user_ids:
            user_ids = list(map(int, user_ids.split(",")))
            query = query.filter(user_id__in=user_ids)
        if country_ids:
            country_ids = list(map(int, country_ids.split(",")))
            query = query.filter(user__location_country_id__in=country_ids)
        if leave_month:
            start = datetime.strptime(leave_month + "-01", "%Y-%m-%d")
            _, last_day_of_month = calendar.monthrange(start.year, start.month)
            end = datetime(
                start.year, start.month, last_day_of_month, 23, 59, 59
            )
            query = query.filter(
                Q(start_date__range=[start, end])
                | Q(end_date__range=[start, end])
                | Q(start_date__lte=start, end_date__gte=end)
                | Q(start_date__gte=start, end_date__lte=end)
            )
        query = query.order_by("-start_date")
        total = query.count()
        if pagable:
            offset = (page_number - 1) * page_size
            limit = page_size
            query = query[offset : offset + limit]

        try:
            partner_leaves = []
            for leave in query.all():
                if not leave.has_delete and leave.has_rejected:
                    status = "Rejected"
                elif (
                    not leave.has_delete
                    and not leave.has_rejected
                    and leave.has_finish
                ):
                    status = "Approved"
                elif (
                    not leave.has_delete
                    and not leave.has_rejected
                    and not leave.has_finish
                ):
                    status = "Pending"
                else:
                    status = "Cancelled"

                partner_leaves.append(
                    dict(
                        leave_id=leave.id,
                        status=status,
                        start_date=leave.start_date.strftime("%Y-%b-%d"),
                        end_date=leave.end_date.strftime("%Y-%b-%d"),
                        days=leave.days,
                        created_on=leave.created_at.strftime("%Y-%b-%d"),
                        updated_on=leave.updated_at.strftime("%Y-%b-%d"),
                        has_attachment=leave.attachemnts.exists(),
                        # leave type
                        leave_type_id=leave.leave_type_id,
                        leave_type_name=leave.leave_type.name,
                        # user info
                        user_id=leave.user_id,
                        user_name=leave.user.name,
                        country_id=leave.user.location_country.id,
                        country_name=leave.user.location_country.name_en,
                    )
                )

            return PartnerLeaveListNode(
                totalCount=total,
                results=partner_leaves,
            )
        except Exception as e:
            raise e

    @staticmethod
    def query_users_in_partner_leaves(partner_id):
        leave_ids = PartnerLeave.objects.filter(
            partner_id=partner_id
        ).values_list("leave_id", flat=True)
        user_ids = (
            UserApplyLeave.objects.filter(id__in=leave_ids)
            .values_list("user_id", flat=True)
            .distinct()
        )
        return user_ids

    @staticmethod
    def resolve_filter_partner_employee_from_leave_list(root, info, **kwargs):
        partner_id = kwargs.get("partner_id")
        name = kwargs.get("name")
        user_ids = ResolverMixin.query_users_in_partner_leaves(partner_id)
        queryset = User.objects.filter(id__in=user_ids).all()
        if name:
            queryset = queryset = filter_encrypt_users(
                name, queryset, queryset
            )
        users = queryset.all()
        rows = []
        for user in users:
            rows.append(
                dict(
                    user_id=user.id,
                    user_name=user.name,
                )
            )
        return FilterPartnerEmployeeFromLeaveListNode(
            totalCount=len(rows),
            results=rows,
        )

    @staticmethod
    def resolve_filter_partner_country_from_leave_list(root, info, **kwargs):
        partner_id = kwargs.get("partner_id")
        name = kwargs.get("name")
        user_ids = ResolverMixin.query_users_in_partner_leaves(partner_id)
        queryset = User.objects.filter(id__in=user_ids)
        if name:
            queryset = queryset.filter(
                location_country__name_en__icontains=name
            )
        users = queryset.values(
            "location_country_id", "location_country__name_en"
        ).distinct()
        rows = []
        for user in users:
            rows.append(
                dict(
                    country_id=user.get("location_country_id"),
                    country_name=user.get("location_country__name_en"),
                )
            )
        return FilterPartnerCountryFromLeaveListNode(
            totalCount=len(rows),
            results=rows,
        )
