from datetime import date

from auditlog.registry import auditlog
from django.core.exceptions import ValidationError
from django.db import models
from django.db.models import UniqueConstraint
from django.utils import timezone

from adm.models import Area
from leave import BALANCE_TYPE, BALANCE_TYPE_P
from utils.basefield import ColorField
from utils.basemodel import BaseModel
from utils.constant import PEO_TYPE, STANDARD_COMPANY_TYPE
from utils.cryptography import Encrypted<PERSON><PERSON><PERSON>ield, EncryptedFileField
from utils.exceptionhandler import ErrMsg
from utils.utils import gen_file_path

CARRYOVER_N = BALANCE_TYPE
CARRYOVER_P = BALANCE_TYPE_P
CARRTOVER = (
    (CARRYOVER_N, "n -> br"),
    (CARRYOVER_P, "pr -> n"),
)


class LeaveType(BaseModel):
    name = models.CharField(max_length=100, db_index=True)
    color = ColorField(default="#cccccc")
    has_common = models.BooleanField(default=False)
    provider = models.ForeignKey(
        "adm.ProviderProfile",
        on_delete=models.CASCADE,
        related_name="leave_types",
    )
    has_indefinite = models.BooleanField(default=False)

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    class Meta:
        ordering = ["id"]


class LeavePolicy(BaseModel):
    name = models.CharField(max_length=100, db_index=True)
    leave_type = models.ManyToManyField(
        LeaveType, related_name="leave_policy", through="LeaveTypePolicyRef"
    )
    provider = models.ForeignKey(
        "adm.ProviderProfile",
        on_delete=models.CASCADE,
        related_name="leave_policies",
    )
    reset_date = models.DateField(
        default=date(timezone.now().year, 1, 1),
        verbose_name="1 January by default",
    )

    def delete(self, using=None, keep_parents=False, logical_del=True):
        self.leavetypepolicyref_set.delete()
        return super().delete(using, keep_parents, logical_del)


class LeaveTypePolicyRef(BaseModel):
    REMAINING_CARRYOVER = 1
    LIMIT_CARRYOVER = 2
    CARRYOVER_TYPE = (
        (REMAINING_CARRYOVER, "Remaining Count"),
        (LIMIT_CARRYOVER, "Limit Count"),
    )

    leave_type = models.ForeignKey(
        LeaveType, db_index=False, on_delete=models.CASCADE
    )
    leave_policy = models.ForeignKey(
        LeavePolicy, db_index=False, on_delete=models.CASCADE
    )
    entitlement = models.PositiveSmallIntegerField(default=0)
    has_carryover = models.BooleanField(default=False)
    expiry_date = models.DateField(null=True)
    carryover_type = models.PositiveSmallIntegerField(
        choices=CARRYOVER_TYPE, default=REMAINING_CARRYOVER
    )
    carryover_days = models.FloatField(
        default=0, verbose_name="Maximum carryover_days"
    )

    class Meta:
        index_together = (("leave_policy", "leave_type"),)


class LeaveProcess(BaseModel):
    PEO = PEO_TYPE
    STANDARD_COMPANY = STANDARD_COMPANY_TYPE
    WORK_FOR_TYPE = (
        (PEO, "PEO Company"),
        (STANDARD_COMPANY, "Standard Company"),
    )
    name = models.CharField(max_length=100, default="")
    work_for = models.PositiveIntegerField(verbose_name="PEO or Team ID")
    work_for_type = models.PositiveSmallIntegerField(
        choices=WORK_FOR_TYPE, default=PEO
    )
    creator = models.ForeignKey(
        "user.User", on_delete=models.CASCADE, related_name="leave_processes"
    )

    class Meta:
        index_together = (("work_for", "work_for_type"),)

    def delete(self, using=None, keep_parents=False, logical_del=True):
        for step in self.steps.all():
            step.delete()
        super().delete(using, keep_parents, logical_del)


class LeaveProcessStep(BaseModel):
    DEFAULT_INDEX = 0
    SECOND_INDEX = 1
    ACTION_INDEX_CHOICE = (
        (DEFAULT_INDEX, "Step 1"),
        (SECOND_INDEX, "Step 2"),
    )
    process = models.ForeignKey(
        LeaveProcess, on_delete=models.CASCADE, related_name="steps"
    )
    is_direct_report = models.BooleanField(default=False)
    user = models.ForeignKey(
        "user.User", on_delete=models.SET_NULL, null=True, related_name="steps"
    )
    group = models.ForeignKey(
        "adm.AdminGroup",
        on_delete=models.CASCADE,
        null=True,
        related_name="steps",
    )
    action_index = models.PositiveSmallIntegerField(
        choices=ACTION_INDEX_CHOICE, default=DEFAULT_INDEX
    )
    next_step = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        null=True,
        related_name="previous_step",
    )


class CountryLeavePolicy(BaseModel):
    CONTRACTUAL_STATUTORY = 1
    CONTRACTUAL_OPTIONAL = 2
    CONTRACTUAL = (
        (CONTRACTUAL_STATUTORY, "Statutory"),
        (CONTRACTUAL_OPTIONAL, "Optional"),
    )

    ENTITLEMENT_DEFINITE = 1
    ENTITLEMENT_INDEFINITE = 2
    ENTITLEMENT = (
        (ENTITLEMENT_DEFINITE, "Definite"),
        (ENTITLEMENT_INDEFINITE, "Indefinite"),
    )
    CARRYOVER_MAXIMUM_DAYS = "MAXIMUM_DAYS"
    CARRYOVER_REMAINING_DAYS = "REMAINING_DAYS"

    CARRYOVER_OPTIONS = [
        (CARRYOVER_MAXIMUM_DAYS, CARRYOVER_MAXIMUM_DAYS),
        (CARRYOVER_REMAINING_DAYS, CARRYOVER_REMAINING_DAYS),
    ]

    country = models.ForeignKey(
        "adm.Area",
        on_delete=models.CASCADE,
        related_name="country_leave_policy",
        verbose_name="Country",
    )
    contractual = models.PositiveSmallIntegerField(
        choices=CONTRACTUAL, default=CONTRACTUAL_STATUTORY
    )
    entitlement = models.PositiveSmallIntegerField(
        choices=ENTITLEMENT, default=ENTITLEMENT_DEFINITE
    )
    standard = models.PositiveSmallIntegerField(null=True)
    min = models.PositiveSmallIntegerField(null=True)
    max = models.PositiveSmallIntegerField(null=True)
    leave_type = models.ForeignKey(
        LeaveType,
        on_delete=models.CASCADE,
        related_name="leave_type",
        verbose_name="LeaveType",
        default=1,
    )
    affect_payroll = models.BooleanField(default=False)
    carryover_enabled = models.BooleanField(
        default=False,
        verbose_name="Carryover Enabled",
        help_text="Whether this leave type can be carried over to the next year.",
    )
    carryover_days_type = models.CharField(
        max_length=20,
        choices=CARRYOVER_OPTIONS,
        default=CARRYOVER_REMAINING_DAYS,
        null=True,
        blank=True,
        verbose_name="Carryover Days Type",
        help_text="Define whether a maximum number of days or all remaining days can be carried over.",
    )
    carryover_max_days = models.PositiveSmallIntegerField(
        null=True,
        blank=True,
        verbose_name="Maximum Days",
        help_text="Maximum number of days that can be carried over if carryover is enabled.",
    )
    cash_out_enabled = models.BooleanField(
        default=False,
        verbose_name="Cash Out Enabled",
        help_text="Whether unused leave needs to be cashed out when carryover is off.",
    )


class UserCarryOverApply(BaseModel):
    user = models.ForeignKey(
        "user.User", on_delete=models.CASCADE, related_name="apply_carryovers"
    )
    leave_type = models.ForeignKey(
        LeaveTypePolicyRef,
        on_delete=models.CASCADE,
        related_name="apply_carryovers",
        verbose_name="LeaveTypePolicyRef ID",
        null=True,
    )

    country_leave_policy = models.ForeignKey(
        CountryLeavePolicy,
        on_delete=models.CASCADE,
        related_name="country_leave_policy",
        verbose_name="CountryLeavePolicy ID",
        null=True,
    )
    days = models.FloatField(default=0, verbose_name="Days requested")
    process_step = models.ForeignKey(
        LeaveProcessStep,
        db_index=False,
        null=True,
        on_delete=models.SET_NULL,
        related_name="apply_carryovers",
    )
    process = models.ForeignKey(
        LeaveProcess,
        db_index=False,
        null=True,
        on_delete=models.SET_NULL,
        related_name="apply_carryovers",
    )
    has_finish = models.BooleanField(default=False)
    has_rejected = models.BooleanField(default=False)
    carryover = models.PositiveSmallIntegerField(
        choices=CARRTOVER, default=CARRYOVER_N
    )

    approval_policy_id = models.IntegerField(default=0)
    approval_step_id = models.IntegerField(default=0)
    approvers = models.JSONField(default=list)

    class Meta:
        index_together = (("process_step", "process"),)
        ordering = ["id"]


class UserApplyLeave(BaseModel):
    HALF_ALL = 0
    HALF_1ST = 1
    HALF_2ND = 2
    HALF_EMPYT = 4
    APPLY_HALF = (
        (HALF_ALL, "All day"),
        (HALF_1ST, "1st half"),
        (HALF_2ND, "2nd half"),
        (HALF_EMPYT, ""),
    )

    user = models.ForeignKey(
        "user.User", on_delete=models.CASCADE, related_name="apply_leaves"
    )
    leave_type = models.ForeignKey(
        LeaveType, on_delete=models.CASCADE, related_name="apply_leaves"
    )
    start_date = models.DateField(verbose_name="Of leave period")
    start_date_half = models.PositiveSmallIntegerField(
        choices=APPLY_HALF, default=HALF_EMPYT
    )
    end_date = models.DateField(verbose_name="Of leave period")
    end_date_half = models.PositiveSmallIntegerField(
        choices=APPLY_HALF, default=HALF_EMPYT
    )
    days = models.FloatField(default=0, verbose_name="Days requested")
    process_step = models.ForeignKey(
        LeaveProcessStep,
        on_delete=models.SET_NULL,
        related_name="apply_leaves",
        null=True,
    )
    process = models.ForeignKey(
        LeaveProcess,
        on_delete=models.SET_NULL,
        related_name="apply_leaves",
        null=True,
    )
    has_finish = models.BooleanField(default=False)
    has_rejected = models.BooleanField(default=False)
    cdays = models.FloatField(
        default=0, verbose_name="Carry-over days requested"
    )

    has_cross = models.BooleanField(default=False)
    br_days = models.FloatField(
        default=0, verbose_name="Days requested, in next cycle"
    )
    pr_days = models.FloatField(
        default=0, verbose_name="Days requested, in last cycle"
    )
    br_cdays = models.FloatField(
        default=0, verbose_name="Carry-over days requested, in next cycle"
    )
    pr_cdays = models.FloatField(
        default=0, verbose_name="Carry-over days requested, in last cycle"
    )

    approval_policy_id = models.IntegerField(default=0)
    approval_step_id = models.IntegerField(default=0)

    APPLY_HALF_MAP = {
        HALF_ALL: "",
        HALF_1ST: "1st Half",
        HALF_2ND: "2nd Half",
    }

    APPLY_HALF_MAP_STR = {
        HALF_ALL: "",
        HALF_1ST: "morning",
        HALF_2ND: "afternoon",
    }

    HALF_DATE = [HALF_2ND, HALF_1ST]

    @property
    def half_start_date(self):
        try:
            return self.APPLY_HALF_MAP[self.start_date_half]
        except KeyError:
            return None

    @property
    def half_end_date(self):
        try:
            return self.APPLY_HALF_MAP[self.end_date_half]
        except KeyError:
            return None

    @property
    def half_start_date_str(self):
        try:
            return self.APPLY_HALF_MAP_STR[self.start_date_half]
        except KeyError:
            return ""

    @property
    def half_end_date_str(self):
        try:
            return self.APPLY_HALF_MAP_STR[self.end_date_half]
        except KeyError:
            return ""

    def delete(self, using=None, keep_parents=False, logical_del=True):
        for attachment in self.attachemnts.all():
            attachment.delete()
        super().delete(using, keep_parents, logical_del)


class UserApplyLeaveHistory(BaseModel):
    ACTION_APPLY = 1
    ACTION_APPROVE = 2
    ACTION_REJECT = 3
    ACTION_CANCEL = 4
    ACTION = (
        (ACTION_APPLY, "Apply"),
        (ACTION_APPROVE, "Approve"),
        (ACTION_REJECT, "Reject"),
        (ACTION_CANCEL, "Cancel"),
    )
    TYPE_LEAVE_APPLY = 1
    TYPE_CARRYOVER_APPLY = 2
    TYPE = (
        (TYPE_LEAVE_APPLY, "From UserApplyLeave"),
        (TYPE_CARRYOVER_APPLY, "From UserCarryOverApply"),
    )

    user_leave_id = models.IntegerField()
    operator_id = models.IntegerField()
    action = models.PositiveSmallIntegerField(
        choices=ACTION, default=ACTION_APPLY
    )
    comments = models.CharField(max_length=225, default="")
    history_type = models.PositiveSmallIntegerField(
        choices=TYPE, default=TYPE_LEAVE_APPLY
    )

    class Meta:
        index_together = (("user_leave_id", "history_type"),)
        ordering = ["-id"]

    def log(
        self,
        leave_id,
        operator_id,
        action,
        comments,
        history_type=TYPE_LEAVE_APPLY,
    ):
        self.user_leave_id = leave_id
        self.operator_id = operator_id
        self.action = action
        self.comments = comments or ""
        self.history_type = history_type
        return self.save()

    @classmethod
    def carrryover_log(cls, leave_id, operator_id, action, comments):
        obj = cls()
        return obj.log(
            leave_id, operator_id, action, comments, cls.TYPE_CARRYOVER_APPLY
        )


class LeaveAttachment(BaseModel):
    leave = models.ForeignKey(
        UserApplyLeave,
        null=True,
        on_delete=models.CASCADE,
        related_name="attachemnts",
    )
    file = EncryptedFileField(upload_to=gen_file_path, default="")
    file_name = EncryptedCharField(max_length=200, default="")
    user_id = models.PositiveIntegerField()

    def delete(self, using=None, keep_parents=False, logical_del=True):
        self.file.delete()
        super().delete(using, keep_parents, logical_del)

    class Meta:
        ordering = ["id"]


class PartnerLeave(BaseModel):
    partner_id = models.IntegerField(db_index=True)
    leave_id = models.IntegerField()


auditlog.register(
    CountryLeavePolicy, exclude_fields=["created_at", "updated_at"]
)


class LeaveCountryPolicyAnnualCycle(BaseModel):
    RESET_YEARLY = "YEARLY"
    RESET_ON_START_DATE = "ON_START_DATE"
    RESET_METHOD_CHOICES = [
        (RESET_YEARLY, "Yearly"),
        (RESET_ON_START_DATE, "On Start Date"),
    ]

    country = models.ForeignKey(
        Area,
        on_delete=models.CASCADE,
        related_name="annual_cycles",
        verbose_name="Country",
    )
    reset_method = models.CharField(
        max_length=20,
        choices=RESET_METHOD_CHOICES,
        default=RESET_YEARLY,
        verbose_name="Reset Method",
        help_text="Defines when the leave balance is reset: Yearly or On Start Date.",
    )
    reset_month = models.PositiveSmallIntegerField(
        null=True,
        blank=True,
        verbose_name="Reset Month",
        help_text="Applicable when reset method is Yearly. Select from Jan to Dec.",
    )
    expiration_enabled = models.BooleanField(
        default=True,
        verbose_name="Expiration Enabled",
        help_text="Indicates whether carried over days will expire or not.",
    )
    expiration_months = models.PositiveSmallIntegerField(
        default=3,
        null=True,
        blank=True,
        verbose_name="Expiration Months",
        help_text="Number of months after the next annual cycle when carried over days expire. Required if expiration_enabled is Yes.",
    )

    class Meta:
        db_table = "leave_country_policy_annual_cycle"
        constraints = [
            UniqueConstraint(
                fields=["country"], name="unique_country_annual_cycle"
            )
        ]
        verbose_name = "Country Leave Policy Annual Cycle"
        verbose_name_plural = "Country Leave Policy Annual Cycles"

    def clean(self):
        """
        Custom validation logic for LeaveCountryPolicyAnnualCycle.
        """

        super().clean()

        # Ensure that only one record exists per country
        if (
            LeaveCountryPolicyAnnualCycle.objects.filter(country=self.country)
            .exclude(pk=self.pk)
            .exists()
        ):
            raise ValidationError(
                ErrMsg.LEAVE_COUNTRY_POLICY_ANNUAL_CYCLE_ALREADY_EXISTS
                % self.country.id
            )

        # Ensure reset_month is set only when reset_method is YEARLY
        if self.reset_method == self.RESET_YEARLY and self.reset_month is None:
            raise ValidationError(
                ErrMsg.LEAVE_COUNTRY_POLICY_ANNUAL_CYCLE_REQUIRED_RESET_MONTH
            )
        if (
            self.reset_method != self.RESET_YEARLY
            and self.reset_month is not None
        ):
            raise ValidationError(
                ErrMsg.LEAVE_COUNTRY_POLICY_ANNUAL_CYCLE_NULL_RESET_MONTH
            )
        # Ensure reset_month is None or between 1 and 12
        if self.reset_month is not None and (
            self.reset_month < 1 or self.reset_month > 12
        ):
            raise ValidationError(
                ErrMsg.LEAVE_COUNTRY_POLICY_ANNUAL_CYCLE_WRONG_RESET_MONTH
            )

        # Validation: If expiration is enabled, expiration_months must be between 1 and 12.
        if self.expiration_enabled and (
            self.expiration_months is None
            or not (1 <= self.expiration_months <= 12)
        ):
            raise ValidationError(
                ErrMsg.LEAVE_COUNTRY_POLICY_ANNUAL_CYCLE_WRONG_EXPIRATION_MONTH
            )

        # Validation: If expiration is disabled, expiration_months must be None.
        if not self.expiration_enabled and self.expiration_months is not None:
            raise ValidationError(
                ErrMsg.LEAVE_COUNTRY_POLICY_ANNUAL_CYCLE_NON_EMPTY_EXPIRATION_MONTH
            )


auditlog.register(
    LeaveCountryPolicyAnnualCycle, exclude_fields=["created_at", "updated_at"]
)
