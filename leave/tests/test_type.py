import json

from utils.basetestcase import BaseGraphQLTestCase


class LeaveTypeTestCase(BaseGraphQLTestCase):
    def test_leave_type_query(self):
        response = self.query(
            """
             query leaveTypeList{
                    leaveTypeList(hasCommon: false) {
                        results {
                             id
                            color
                            name
                        }
                         totalCount
                    }
            }
            """,
            operation_name="leaveTypeList",
        )
        self.assertResponseNoErrors(response)

    def test_leave_type_mutation(self):
        # save
        response = self.query(
            """
            mutation saveLeaveType($input: SaveLeaveTypeInput!) {
                saveLeaveType(data: $input) {
                        status
                        message
                        leaveType{
                            id
                        }
                        __typename

                }
            }

            """,
            operation_name="saveLeaveType",
            input_data={
                "name": "TESTING",
                "color": "#cccccc",
                "hasCommon": True,
            },
        )
        self.assertResponseNoErrors(response)
        content = json.loads(response.content)
        leave_type_id = content["data"]["saveLeaveType"]["leaveType"]["id"]
        # update
        response = self.query(
            """
            mutation updateLeaveType($data: UpdateLeaveTypeInput!, $id: ID!) {
                updateLeaveType(data: $data, id: $id) {
                        status
                        message
                        __typename
                }
            }

            """,
            operation_name="updateLeaveType",
            variables={
                "data": {"name": "TESTING2", "hasCommon": False},
                "id": leave_type_id,
            },
        )
        self.assertResponseNoErrors(response)
        # delete
        response = self.query(
            """
            mutation deleteLeaveType($id: ID!) {
                deleteLeaveType(id: $id) {
                        status
                        message
                        __typename
                }
            }

            """,
            operation_name="deleteLeaveType",
            variables={
                "id": leave_type_id,
            },
        )
        self.assertResponseNoErrors(response)
