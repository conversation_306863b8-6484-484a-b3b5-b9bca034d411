# Generated by Django 3.1 on 2021-09-07 05:14

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("leave", "0004_auto_20210823_0327"),
    ]

    operations = [
        migrations.AddField(
            model_name="leaveprocess",
            name="creator",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="leave_processes",
                to="user.user",
            ),
            preserve_default=False,
        ),
        migrations.AlterModelOptions(
            name="leavetype",
            options={"ordering": ["id"]},
        ),
    ]
