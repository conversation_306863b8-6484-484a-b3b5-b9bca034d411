# Generated by Django 3.1 on 2021-07-30 03:31

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("adm", "0012_auto_20210713_0544"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("leave", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="LeaveProcess",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("has_delete", models.BooleanField(default=False)),
                ("name", models.CharField(default="", max_length=100)),
                (
                    "work_for",
                    models.PositiveIntegerField(verbose_name="PEO or Team ID"),
                ),
                (
                    "work_for_type",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "PEO Company"), (2, "Standard Company")],
                        default=1,
                    ),
                ),
            ],
            options={
                "index_together": {("work_for", "work_for_type")},
            },
        ),
        migrations.CreateModel(
            name="LeaveProcessStep",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_direct_report", models.BooleanField(default=False)),
                (
                    "action_index",
                    models.PositiveSmallIntegerField(
                        choices=[(0, "Step 1"), (1, "Step 2")], default=0
                    ),
                ),
                (
                    "group",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="steps",
                        to="adm.admingroup",
                    ),
                ),
                (
                    "process",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="steps",
                        to="leave.leaveprocess",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="steps",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
