from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from rest_framework.exceptions import PermissionDenied
from rest_framework.request import Request
from rest_framework_simplejwt.authentication import JWTAuthentication

from app.client.schemas.client import (
    ClientAmendmentAutomationRequest,
    ClientAmendmentAutomationResponse,
    ClientDetailResponse,
    ClientDisabledAmendmentAutomationResponse,
    ClientDivisionResponse,
    ClientDivisionsResponse,
    ClientDocumentsResponse,
    ClientPaymentProofResponse,
    ClientPayslipAndAgreementDocumentsResponse,
    ClientResponse,
    ClientSimpleInfoResponse,
    ClientUserDocumentsResponse,
    ContactSupportRequest,
    CreateClientRequest,
    CreateClientRequestEx,
    LogoResponse,
    SearchClientRequest,
    SyncClientAMRequest,
    UpdateClientRequest,
    UpdateClientSkipDepositRequest,
)
from app.client.schemas.client_configuration import (
    ClientConfigurationResponse,
    SaveClientConfigurationRequest,
)
from app.client.schemas.client_multiplier import (
    ClientMultiplierDetailListResponse,
    SaveClientMultiplierListRequest,
)
from app.client.serializers.client import (
    ClientDepositOptionResponse,
    UpdateClientDepositOptionRequest,
)
from app.client.services import (
    client_configuration_service,
    client_multiplier_service,
    client_service,
)
from app.constants import JoinPermissionEnum
from app.pricing_bundle.services import pricing_bundle_service
from app.user.services import user_division_service
from utils.authentications import ApiKeyAuthentication
from utils.constant import Permission
from utils.djangoplus.schemas import (
    get_request_schema,
    get_response_schema,
    pagination_parameters,
)
from utils.djangoplus.views import UserViewSet
from utils.permission import check_permissions, permissions_checker


class ClientViewSet(UserViewSet):
    name = "clients"

    @swagger_auto_schema(
        operation_summary="Search client (Private)",
        tags=["join", "client"],
        # request_body=get_request_schema(SearchClientRequest),
        responses={200: get_response_schema(ClientResponse, page=True)},
    )
    @action(
        methods=["post"],
        url_path="search",
        detail=False,
        authentication_classes=[ApiKeyAuthentication],
        permission_classes=[],
    )
    def search_client(self, request: Request):
        input = self.parse_body(request, SearchClientRequest)
        pagination_query = self.get_pagination_query(request=request)
        input.pagable = pagination_query.pagable
        input.page_size = pagination_query.page_size
        input.page_number = pagination_query.page_number
        resp = client_service.list(input)
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="Get client detail",
        tags=["join", "client"],
        responses={200: get_response_schema(ClientDetailResponse)},
    )
    def retrieve(self, request: Request, pk):
        user = request.user
        resp = client_service.detail(int(pk), user)
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="Update client detail",
        tags=["join", "client"],
        request_body=get_request_schema(UpdateClientRequest),
        responses={200: get_response_schema(ClientDetailResponse)},
    )
    def update(self, request: Request, pk):
        user = request.user
        data = self.parse_body(request, UpdateClientRequest)
        resp = client_service.update(int(pk), data, user)
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="Upload client logo",
        tags=["join", "client"],
        responses={200: get_response_schema(LogoResponse)},
    )
    @action(methods=["post"], url_path="upload_logo", detail=False)
    @permissions_checker(
        permissions=[
            JoinPermissionEnum.CLIENT_GENERAL_INFO_FULL_ACCESS,
            Permission.TEAM_FULLACCESS,
        ]
    )
    def upload_logo(self, request: Request):
        file = request.data.get("file")
        assert file, "mandatory parameters: file"
        resp = client_service.upload_logo(file)
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="Create client",
        tags=["join", "client"],
        request_body=get_request_schema(CreateClientRequest),
        responses={200: get_response_schema(ClientResponse)},
    )
    @action(
        methods=["post"],
        url_path="register",
        detail=False,
        authentication_classes=[],
        permission_classes=[],
    )
    def register(self, request: Request):
        """
        There is protection in logic
        """
        data = self.parse_body(request, CreateClientRequest)
        resp = client_service.register(data)
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="add new client on juno",
        tags=["juno", "client"],
        request_body=get_request_schema(CreateClientRequestEx),
        responses={200: get_response_schema(ClientResponse)},
    )
    @action(
        methods=["post"],
        url_path="add-new",
        detail=False,
    )
    @permissions_checker(
        permissions=[
            Permission.TEAM_FULLACCESS,
        ]
    )
    def add_on_juno(self, request: Request):
        user = request.user
        data = self.parse_body(request, CreateClientRequestEx)

        team = client_service.add_on_juno(
            user, data, request.headers.get("Authorization")
        )

        pricing_bundle_service.create_customer(
            name=team.name, horizons_team_id=team.id
        )
        if data.plan_code:
            pricing_bundle_service.subscribe(
                horizons_team_id=team.id, plan_code=data.plan_code
            )
        return self.response(team)

    @swagger_auto_schema(
        operation_summary="sync client's am_id when raising new hire",
        tags=["join", "client"],
        request_body=get_request_schema(SyncClientAMRequest),
        responses={200: get_response_schema(ClientResponse)},
    )
    @action(
        methods=["put"],
        url_path="update-am",
        detail=False,
        authentication_classes=[ApiKeyAuthentication],
        permission_classes=[],
    )
    def sync_client_am(self, request: Request):
        data = self.parse_body(request, SyncClientAMRequest)
        resp = client_service.sync_client_am(data)
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="set client active",
        tags=["join", "client"],
    )
    @action(
        methods=["put"],
        url_path="active",
        detail=True,
        authentication_classes=[ApiKeyAuthentication],
        permission_classes=[],
    )
    def set_client_active(self, request: Request, pk):
        resp = client_service.set_client_active(pk)
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="check client phase",
        tags=["join", "client"],
    )
    @action(
        methods=["put"],
        url_path="check-phase",
        detail=True,
        authentication_classes=[ApiKeyAuthentication],
        permission_classes=[],
    )
    def check_client_phase(self, request: Request, pk):
        resp = client_service.check_client_phase(pk)
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="Contact support",
        tags=["join", "client"],
        request_body=get_request_schema(ContactSupportRequest),
    )
    @action(
        methods=["post"],
        url_path="contact-support",
        detail=False,
    )
    def contact_support(self, request: Request):
        user = request.user
        data = self.parse_body(request, ContactSupportRequest)
        resp = client_service.contact_support(user, data)
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="Client documents",
        tags=["join", "client", "documents"],
        manual_parameters=pagination_parameters(
            [
                openapi.Parameter(
                    "file_type",
                    openapi.IN_QUERY,
                    type=openapi.TYPE_STRING,
                    description="1->Service Agreement; 2->Pricing Schedule; 3->Other",
                ),
            ]
        ),
        responses={
            200: get_response_schema(ClientDocumentsResponse, page=True)
        },
    )
    @action(
        methods=["get"],
        url_path="documents",
        detail=True,
    )
    @permissions_checker(
        permissions=[
            JoinPermissionEnum.CLIENT_DOCUMENTS_FULL_ACCESS,
        ]
    )
    def query_client_documents(self, request: Request, pk):
        page = self.get_pagination_query(request)
        _file_type = request.query_params.get("file_type")
        file_type = list(map(int, _file_type.split(","))) if _file_type else []
        resp = client_service.query_documents(
            page, pk, file_type, request.user
        )
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="Client user documents",
        tags=["join", "client", "documents"],
        manual_parameters=pagination_parameters(
            [
                openapi.Parameter(
                    "kind",
                    openapi.IN_QUERY,
                    type=openapi.TYPE_STRING,
                    description="1->Other; 2->Payslip; 3->Labor Contract; 4->Timesheet",
                ),
            ]
        ),
        responses={
            200: get_response_schema(ClientUserDocumentsResponse, page=True)
        },
    )
    @action(
        methods=["get"],
        url_path="user-documents",
        detail=True,
    )
    @permissions_checker(
        permissions=[
            JoinPermissionEnum.CLIENT_DOCUMENTS_FULL_ACCESS,
            JoinPermissionEnum.EMPLOYEE_MYFILES_FULL_ACCESS,
            JoinPermissionEnum.CONTRACTOR_MYFILES_FULL_ACCESS,
        ]
    )
    def query_client_user_documents(self, request: Request, pk):
        page = self.get_pagination_query(request)
        user_id = request.query_params.get("user_id")
        query_month = request.query_params.get("query_month")
        token = request.headers.get("Authorization")
        kind = request.query_params.get("kind")
        resp = client_service.query_user_documents(
            user=request.user,
            page=page,
            client_id=pk,
            auth_token=token,
            user_id=user_id,
            query_month=query_month,
            kind=kind,
        )
        return self.response(resp)

    @action(
        methods=["get"],
        url_path="user-documents/user_names",
        detail=True,
    )
    @permissions_checker(
        permissions=[
            JoinPermissionEnum.CLIENT_DOCUMENTS_FULL_ACCESS,
            JoinPermissionEnum.EMPLOYEE_MYFILES_FULL_ACCESS,
            JoinPermissionEnum.CONTRACTOR_MYFILES_FULL_ACCESS,
        ]
    )
    def query_client_user_documents_names(self, request: Request, pk):
        token = request.headers.get("Authorization")
        kind = request.query_params.get("kind")
        name = request.query_params.get("name")
        resp = client_service.query_user_documents_user_names(
            request.user,
            pk,
            token,
            kind,
            name,
        )
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="Client Payslip documents",
        tags=["join", "client", "documents", "payslip"],
        manual_parameters=pagination_parameters(
            [
                openapi.Parameter(
                    "user_id",
                    openapi.IN_QUERY,
                    type=openapi.TYPE_STRING,
                    description="user id",
                ),
            ]
        ),
        responses={
            200: get_response_schema(
                ClientPayslipAndAgreementDocumentsResponse, page=True
            )
        },
    )
    @action(
        methods=["get"],
        url_path="payslips",
        detail=True,
    )
    @permissions_checker(
        permissions=[
            JoinPermissionEnum.CLIENT_DOCUMENTS_FULL_ACCESS,
            JoinPermissionEnum.EMPLOYEE_MYFILES_FULL_ACCESS,
            JoinPermissionEnum.CONTRACTOR_MYFILES_FULL_ACCESS,
        ]
    )
    def query_client_payslips(self, request: Request, pk):
        page = self.get_pagination_query(request)
        user_id = request.query_params.get("user_id")
        user = request.user
        query_month = request.query_params.get(
            "query_month"
        )  # format: 2023-01
        resp = client_service.query_client_payslips(
            user,
            page,
            pk,
            user_id,
            query_month,
        )
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="Client Payslip Users",
        tags=["join", "client", "documents", "payslip"],
        manual_parameters=pagination_parameters(
            [
                openapi.Parameter(
                    "name",
                    openapi.IN_QUERY,
                    type=openapi.TYPE_STRING,
                    description="user name",
                ),
            ]
        ),
        responses={
            200: get_response_schema(
                ClientPayslipAndAgreementDocumentsResponse, page=True
            )
        },
    )
    @action(
        methods=["get"],
        url_path="payslips/users",
        detail=True,
    )
    @permissions_checker(
        permissions=[
            JoinPermissionEnum.CLIENT_DOCUMENTS_FULL_ACCESS,
            JoinPermissionEnum.EMPLOYEE_MYFILES_FULL_ACCESS,
            JoinPermissionEnum.CONTRACTOR_MYFILES_FULL_ACCESS,
        ]
    )
    def query_client_payslips_users(self, request: Request, pk):
        name = request.query_params.get("name")
        resp = client_service.query_client_payslips_users(
            request.user,
            pk,
            name,
        )
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="Search clients simple info",
        tags=["join", "client"],
        responses={
            200: get_response_schema(ClientSimpleInfoResponse, page=True)
        },
    )
    @action(
        methods=["get"],
        url_path="simple/info",
        detail=False,
        authentication_classes=[JWTAuthentication, ApiKeyAuthentication],
        permission_classes=[],
    )
    def search_client_infos(self, request: Request):
        page = self.get_pagination_query(request)
        ids = request.query_params.get("ids")
        name = request.query_params.get("name")
        resp = client_service.get_simple_info(page, name, ids)
        return self.response(resp)

    # TODO: JOIN-9715 AUTO_DEBITNOTE_WITH_PREREGISTRATION disabled. When all clients are using
    #       AUTO_DEBITNOTE_WITH_PREREGISTRATION, update-skip-deposit and show-skip-deposit should be deleted
    @swagger_auto_schema(
        operation_summary="update client show_skip_deposit",
        tags=["juno", "client"],
    )
    @action(
        methods=["put"],
        url_path="update-skip-deposit",
        detail=True,
    )
    @permissions_checker(permissions=[Permission.JUNO_ONBOARDING_SKIP_DEPOSIT])
    def update_show_skip_deposit(self, request: Request, pk):
        data = self.parse_body(request, UpdateClientSkipDepositRequest)
        resp = client_service.update_show_skip_deposit(pk, data)
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="query client show_skip_deposit",
        tags=["juno", "client"],
    )
    @action(
        methods=["get"],
        url_path="show-skip-deposit",
        detail=True,
    )
    @permissions_checker(permissions=[Permission.ADMIN_CLIENTUSER_VIEW])
    def query_show_skip_deposit(self, request: Request, pk):
        return self.response(dict(show_skip_deposit=False))

    # JOIN-9715 AUTO_DEBITNOTE_WITH_PREREGISTRATION enabled
    @swagger_auto_schema(
        operation_summary="Update client deposit option",
        request_body=UpdateClientDepositOptionRequest,
        tags=["juno", "client"],
    )
    @action(
        methods=["put"],
        url_path="update-deposit-option",
        detail=True,
    )
    @permissions_checker(permissions=[Permission.JUNO_ONBOARDING_SKIP_DEPOSIT])
    def update_deposit_option(self, request: Request, pk):
        serializer = UpdateClientDepositOptionRequest(
            data=request.data, context={"allow_no_deposit": False}
        )
        serializer.is_valid(raise_exception=True)

        resp = client_service.update_deposit_option(
            pk, serializer.validated_data
        )
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="Query client deposit option",
        responses={200: ClientDepositOptionResponse},
        tags=["juno", "client"],
    )
    @action(
        methods=["get"],
        url_path="deposit-option",
        detail=True,
    )
    @permissions_checker(
        permissions=[
            Permission.ADMIN_CLIENTUSER_VIEW,
            JoinPermissionEnum.CLIENT_NEW_HIRE_FULL_ACCESS,
            JoinPermissionEnum.JOIN_EMPLOYEE_MY_ONBOARDING_FULL_ACCESS,
        ],
        per_test_check=True,
    )
    def query_deposit_option(self, request: Request, pk):
        if not check_permissions(
            request.user,
            [Permission.ADMIN_CLIENTUSER_VIEW],
            per_test_check=True,
        ):
            if request.user.team_id != int(pk):
                raise PermissionDenied(
                    "You do not have permission to view this client’s deposit option."
                )

        resp = client_service.query_deposit_option(pk)
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="get client upload payment proof list",
        tags=["juno", "client"],
        responses={
            200: get_response_schema(ClientPaymentProofResponse, page=True)
        },
    )
    @action(
        methods=["get"],
        url_path="payment-proof",
        detail=True,
    )
    @permissions_checker(permissions=[Permission.ADMIN_CLIENTUSER_VIEW])
    def payment_proof(
        self,
        request: Request,
        pk,
    ):
        page = self.get_pagination_query(request)
        resp = client_service.get_payment_proof(page, pk)
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="get client multiplier setting",
        tags=["juno", "client"],
        responses={
            200: get_response_schema(ClientMultiplierDetailListResponse)
        },
    )
    @action(
        methods=["get"],
        url_path="active-multiplier",
        detail=True,
    )
    @permissions_checker(
        permissions=[
            Permission.TEAM_FULLACCESS,
        ]
    )
    def active_multiplier(
        self,
        request: Request,
        pk,
    ):
        resp = (
            client_multiplier_service.active_and_upcoming_version_multiplier(
                client_id=pk,
                serialize=True,
            )
        )
        return self.response(ClientMultiplierDetailListResponse(list=resp))

    @swagger_auto_schema(
        operation_summary="save client multiplier setting",
        tags=["juno", "client"],
        request_body=get_request_schema(SaveClientMultiplierListRequest),
        responses={
            200: get_response_schema(ClientMultiplierDetailListResponse)
        },
    )
    @action(
        methods=["post"],
        url_path="save-multiplier",
        detail=True,
    )
    @permissions_checker(
        permissions=[
            Permission.TEAM_FULLACCESS,
        ]
    )
    def save_multiplier(
        self,
        request: Request,
        pk,
    ):
        user = request.user
        data = self.parse_body(request, SaveClientMultiplierListRequest)
        resp = client_multiplier_service.bulk_save(
            client_id=pk,
            req=data,
            operator_id=user.id,
        )
        return self.response(ClientMultiplierDetailListResponse(list=resp))

    @swagger_auto_schema(
        operation_summary="Get histories of client multiplier setting",
        tags=["juno", "client"],
        responses={
            200: get_response_schema(ClientMultiplierDetailListResponse)
        },
    )
    @action(
        methods=["get"],
        url_path="multiplier-history",
        detail=True,
    )
    @permissions_checker(
        permissions=[
            Permission.TEAM_FULLACCESS,
        ]
    )
    def multiplier_history(
        self,
        request: Request,
        pk,
    ):
        tz = request.query_params.get("tz")
        resp = client_multiplier_service.history_list(
            client_id=pk,
            tz=tz,
        )
        return self.response(ClientMultiplierDetailListResponse(list=resp))

    @swagger_auto_schema(
        operation_summary="Client divisions",
        tags=["join", "client", "divisions"],
        manual_parameters=[
            openapi.Parameter(
                "keyword",
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Search divisions by name keyword",
                required=False,
            ),
        ],
        responses={200: get_response_schema(ClientDivisionsResponse)},
    )
    @action(
        methods=["get"],
        url_path="divisions",
        detail=True,
    )
    @permissions_checker(
        permissions=[
            JoinPermissionEnum.CLIENT_GENERAL_INFO_FULL_ACCESS,
            Permission.TEAM_FULLACCESS,
        ]
    )
    def query_client_divisions(self, request: Request, pk):
        keyword = request.query_params.get("keyword")
        divisions = user_division_service.get_client_divisions(
            request.user, keyword
        )
        return self.response(
            ClientDivisionsResponse(
                rows=[ClientDivisionResponse.from_model(m) for m in divisions],
                total=len(divisions),
            )
        )

    @swagger_auto_schema(
        operation_summary="Client salary increase automation",
        tags=["profile"],
        responses={
            200: get_response_schema(ClientAmendmentAutomationResponse)
        },
    )
    @action(
        methods=["get"],
        url_path="salary-increase-automation",
        detail=True,
    )
    @permissions_checker(
        permissions=[
            Permission.TEAM_FULLACCESS,
        ]
    )
    def salary_increase_automation(self, request: Request, pk):
        resp = client_service.get_salary_increase_automation(
            pk, request=request
        )
        return self.response(ClientAmendmentAutomationResponse(data=resp))

    @swagger_auto_schema(
        operation_summary="Client salary increase automation edit",
        tags=["profile"],
        responses={
            200: get_response_schema(ClientAmendmentAutomationResponse)
        },
        request_body=get_request_schema(ClientAmendmentAutomationRequest),
    )
    @action(
        methods=["put"],
        url_path="save-salary-increase-automation",
        detail=True,
    )
    @permissions_checker(
        permissions=[
            Permission.TEAM_FULLACCESS,
        ]
    )
    def update_salary_increase_automation(self, request: Request, pk):
        data = self.parse_body(request, ClientAmendmentAutomationRequest)
        resp = client_service.update_salary_increase_automation(
            pk, data=data, request=request
        )
        return self.response(ClientAmendmentAutomationResponse(data=resp))

    @swagger_auto_schema(
        operation_summary="Client disabled salary increase automation",
        tags=["profile"],
        responses={
            200: get_response_schema(ClientDisabledAmendmentAutomationResponse)
        },
    )
    @action(
        methods=["get"],
        url_path="disabled-salary-increase-automation",
        detail=True,
    )
    @permissions_checker(
        permissions=[
            JoinPermissionEnum.EMPLOYEE_PROFILE_ACTION_FULL_ACCESS,
            JoinPermissionEnum.CLIENT_ADMIN_TOOLS_FULL_ACCESS,
        ]
    )
    def get_client_disabled_salary_increase_automation(
        self, request: Request, pk
    ):
        resp = client_service.get_disabled_country_ids_for_salary_increase_automation(
            pk
        )
        return self.response(
            ClientDisabledAmendmentAutomationResponse(data=resp)
        )

    @swagger_auto_schema(
        operation_summary="Get client trupay billing setting",
        tags=["juno", "client"],
        responses={200: get_response_schema(ClientConfigurationResponse)},
    )
    @action(
        methods=["get"],
        url_path="trupay-billing",
        detail=True,
    )
    @permissions_checker(
        permissions=[
            Permission.TEAM_FULLACCESS,
            Permission.JUNO_TRU_PAY_BILLING_FULL_ACCESS,
        ]
    )
    def get_trupay_setting(self, request: Request, pk):
        config = client_configuration_service.get_client_configuration(
            client_id=pk,
            config_name=client_configuration_service.CONFIG_NAME.TRU_PAY_BILLING,
        )
        if not config:
            return self.response(None)
        resp = ClientConfigurationResponse.from_model(config)
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="Save client trupay billing setting",
        tags=["juno", "client"],
        request_body=get_request_schema(SaveClientConfigurationRequest),
        responses={200: get_response_schema(ClientConfigurationResponse)},
    )
    @action(
        methods=["post"],
        url_path="trupay-billing",
        detail=False,
    )
    @permissions_checker(
        permissions=[
            Permission.JUNO_TRU_PAY_BILLING_FULL_ACCESS,
        ]
    )
    def save_trupay_setting(self, request: Request):
        req = self.parse_body(request, SaveClientConfigurationRequest)
        config = client_configuration_service.save_client_configuration(
            client_id=req.client_id,
            config_name=client_configuration_service.CONFIG_NAME.TRU_PAY_BILLING,
            config_desc=req.config_desc or "",
            config_value=req.config_value or "",
            is_enabled=req.is_enabled,
            is_global=req.is_global,
        )
        resp = ClientConfigurationResponse.from_model(config)
        return self.response(resp)
