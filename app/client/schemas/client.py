import datetime
from typing import List, Optional

from django.core.exceptions import ValidationError
from pydantic import BaseModel, EmailStr, Field, validator

from adm.models import Attachment, Team
from app.client.constants import ContactSupportType
from app.client.utils import FREE_EMAIL_DOMAINS
from app.errors import SelfRegistrationBizError
from app.user.models.division import Division
from user.models import User
from utils.api_exception import APPExceptionEx
from utils.djangoplus import validate_password
from utils.djangoplus.schemas import PaginationRequest


class UserResponse(BaseModel):
    id: int
    firstname: str
    lastname: str
    email: str

    @classmethod
    def from_model(cls, user: User):
        return UserResponse(
            id=user.id,
            firstname=user.firstname,
            lastname=user.lastname,
            email=user.email,
        )


class SearchClientRequest(PaginationRequest):
    name: Optional[str] = None
    ids: Optional[List[int]] = None


class QueryClientRequest(BaseModel):
    name: Optional[str] = None
    ids: Optional[List[int]] = None


class ClientResponse(BaseModel):
    id: int
    name: str
    hb_id: Optional[str] = None
    account_manager: Optional[UserResponse] = None
    sm: Optional[UserResponse] = None

    @classmethod
    def from_model(cls, client: Team):
        return ClientResponse(
            id=client.id,
            name=client.name,
            hb_id=client.hb_id,
            sm=UserResponse.from_model(client.sm) if client.sm else None,
            account_manager=UserResponse.from_model(client.am)
            if client.am
            else None,
        )


class UpdateClientRequest(BaseModel):
    name: Optional[str] = None
    logo_id: Optional[int] = None

    @validator("name", allow_reuse=True)
    def validate_name(cls, v: Optional[str]):
        if not v:
            return v
        return v.strip()


class LogoResponse(BaseModel):
    id: int
    name: str
    file_type: int
    file: str

    @classmethod
    def from_model(cls, logo: Attachment):
        return LogoResponse(
            id=logo.id,
            name=logo.name,
            file_type=logo.file_type,
            file=logo.file.url,
        )


class ClientDetailResponse(BaseModel):
    id: int
    name: str
    hb_id: str = None
    logo: Optional[LogoResponse] = None
    account_manager: Optional[UserResponse] = None
    sales_manager: Optional[UserResponse] = None
    is_payment_enable: Optional[bool] = False

    @classmethod
    def _get_logo(cls, client: Team) -> Optional[Attachment]:
        logo_queryset = Attachment.objects.filter(
            file_type=Attachment.FILE_TYPE_TEAM_LOGO
        )
        if client.logo_id and logo_queryset.filter(id=client.logo_id).exists():
            return logo_queryset.get(id=client.logo_id)
        return None

    @classmethod
    def from_model(cls, client: Team):
        resp = ClientDetailResponse(
            id=client.id,
            name=client.name,
            hb_id=client.hb_id,
        )

        if client.logo_id:
            logo = cls._get_logo(client)
            resp.logo = LogoResponse.from_model(logo) if logo else None

        if client.am_id:
            resp.account_manager = UserResponse.from_model(client.am)

        if client.sm_id:
            resp.sales_manager = UserResponse.from_model(client.sm)

        return resp


class CreateClientRequest(BaseModel):
    name: str = Field(min_length=1, max_length=100)
    firstname: str = Field(min_length=1, max_length=50)
    lastname: str = Field(min_length=1, max_length=50)
    email: EmailStr
    password: str
    is_agree: bool

    job_title: str = Field(min_length=1, max_length=100)

    legal_entity_name: str = Field(min_length=1, max_length=200)
    registration_num: Optional[str] = Field(max_length=20, default=None)

    billing_currency: str = Field(min_length=1, max_length=20)
    country_id: int = Field(None)
    city_id: Optional[int] = Field(default=None)
    street: str = Field(min_length=1, max_length=200)
    zip: str = Field(min_length=1, max_length=20)

    # invite code
    code: str = Field(min_length=1, max_length=50)

    @validator("password")
    def check_password(cls, v: str):
        v = v.strip()
        try:
            validate_password(v)
        except ValidationError:
            raise ValueError(
                "Your password must be a mixture of digits,letters and symbols.\
             And the length of your password must be between 8 and 20."
            )
        return v

    @validator("is_agree")
    def check_agree(cls, v: bool):
        if not v:
            raise ValueError("Need to agree the registration agreement")
        return v

    @validator("email")
    def check_email_length(cls, v: str):
        if len(v) >= 100:
            raise ValueError("Email limit 100 characters")
        return v


class AgreementRequest(BaseModel):
    id: int = Field(default="service agreement file ID")
    file_name: str = Field(default="service agreement file name")


class CreateClientRequestEx(BaseModel):
    name: str = Field(min_length=1, max_length=100, description="company name")
    hb_id: str = Field(
        min_length=1, max_length=50, description="hubspot company ID"
    )
    logo_id: Optional[int] = Field(default=0, description="logo file ID")

    legal_entity_name: str = Field(min_length=1, max_length=200)
    billing_currency: str = Field(min_length=1, max_length=20)
    registration_num: str = Field(min_length=1, max_length=50)
    pricing_group_id: Optional[int] = Field(default=None)

    # registered addresses
    country_id: int
    city_id: int = Field(default=None)
    street: str = Field(min_length=1, max_length=200)
    zip: str = Field(min_length=1, max_length=20)

    # service agreement
    agreement: List[AgreementRequest]

    # Finance contact
    contact_name: Optional[str] = Field(
        max_length=100, default="", description="Billing Group contact_name"
    )
    contact_emails: str = Field(
        default="", description="Billing Group contact_emails"
    )

    plan_code: Optional[str] = Field(
        default=None, description="Lago plan code for client to subscribe to"
    )


class SelfRegistrationRequest(BaseModel):
    token: str
    contact_name: Optional[str] = Field(
        max_length=100,
        default="",
        description="Primary contact name for the self-registered client",
    )
    contact_email: EmailStr = Field(
        default="",
        description="Business email address of the self-registered client contact",
    )

    @validator("contact_email")
    def validate_business_email(cls, email: str) -> str:
        domain = email.split("@")[-1].lower()
        if domain in FREE_EMAIL_DOMAINS:
            raise APPExceptionEx(
                SelfRegistrationBizError.NOT_BUSINESS_EMAIL,
                err_msg=["Please use a business email address"],
            )
        return email


class SyncClientAMRequest(BaseModel):
    client_id: int = Field(default="client ID")
    am_id: int = Field(default="account manager ID")


class YearMonthRequest(BaseModel):
    year: int = Field(default="year")
    month: int = Field(default="month")


class EmployeeNamesResponse(BaseModel):
    names: Optional[List[str]] = None


class ContactSupportRequest(BaseModel):
    message: str
    type: ContactSupportType = Field(ContactSupportType.Common)


class ClientDocumentsResponse(BaseModel):
    id: int
    file_type: str
    file_name: str
    file_url: str
    create_at: Optional[datetime.datetime]
    uploaded_to: str = Field(default="OSS")


class ClientSimpleInfoResponse(BaseModel):
    id: int
    name: str

    @classmethod
    def from_model(cls, client: Team):
        return cls(
            id=client.id,
            name=client.name,
        )


class ClientUserDocumentUserResponse(BaseModel):
    id: int
    name: str

    @classmethod
    def from_model(cls, user: User):
        return cls(id=user.id, name=user.name)


class ClientUserDocumentsResponse(BaseModel):
    id: int
    user: Optional[ClientUserDocumentUserResponse]
    file_type: str
    file_name: str
    file_url: str
    create_at: Optional[datetime.datetime]
    update_at: Optional[datetime.datetime]
    date: Optional[str]


class ClientDivisionResponse(BaseModel):
    id: int
    name: str

    @classmethod
    def from_model(cls, division: Division):
        return cls(
            id=division.id,
            name=division.name,
        )


class ClientDivisionsResponse(BaseModel):
    total: int
    rows: List[ClientDivisionResponse] = []


class ClientPayslipAndAgreementDocumentsResponse(BaseModel):
    id: int
    file_name: str
    file_url: str
    user_id: int
    firstname: str
    lastname: str
    created_at: Optional[datetime.datetime]
    updated_at: Optional[datetime.datetime]


class UpdateClientSkipDepositRequest(BaseModel):
    show_skip_deposit: bool = Field(
        default=False, description="show skip deposit"
    )


class ClientPaymentProofResponse(BaseModel):
    id: int
    file_name: str
    client_id: int
    created_at: Optional[datetime.datetime]
    updated_at: Optional[datetime.datetime]


class ClientAmendmentAutomation(BaseModel):
    id: int
    country_id: int
    country_name: str
    created_at: Optional[datetime.datetime] = None
    updated_at: Optional[datetime.datetime] = None
    deleted_at: Optional[datetime.datetime] = None
    is_salary_increase_auto: bool
    salary_increase_template: Optional[str] = None
    disabled: bool

    @classmethod
    def from_model(
        cls,
        disabled: bool,
        country_name: str,
        id: int,
        country_id: int,
        created_at: Optional[datetime.datetime],
        updated_at: Optional[datetime.datetime],
        deleted_at: Optional[datetime.datetime],
        is_salary_increase_auto: bool,
        salary_increase_template: Optional[str],
    ):
        return cls(
            disabled=disabled,
            country_name=country_name,
            id=id,
            country_id=country_id,
            created_at=created_at,
            updated_at=updated_at,
            deleted_at=deleted_at,
            is_salary_increase_auto=is_salary_increase_auto,
            salary_increase_template=salary_increase_template,
        )


class ClientAmendmentAutomationResponse(BaseModel):
    data: List[ClientAmendmentAutomation] = []


class ClientDisabledAmendmentAutomationResponse(BaseModel):
    data: List[int] = []


class ClientAmendmentAutomationRequest(BaseModel):
    disabled: List[int] = None
