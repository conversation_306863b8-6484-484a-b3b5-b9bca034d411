from decimal import Decimal
from typing import Optional

from django.utils import timezone

from app.errors import BillingV2BizError
from pay.models import (
    CompensationAttachment,
    VariablePay,
    VariablePayHistory,
    VariablePayType,
)
from user.models import User
from utils.api_exception import APPException
from utils.basemodel import BaseManager


class NonRecurringRepository:
    # region Query
    def get_by_id(
        self,
        pk: int,
        raise_error=False,
    ):
        non_recurring = VariablePay.objects.filter(
            pk=pk,
            category=VariablePay.CATEGORY_INVOICE_PAYROLL,
        ).first()
        if not non_recurring and raise_error:
            raise APPException(BillingV2BizError.NON_RECURRING_NOT_FOUND)
        return non_recurring

    def get_one_by_user_and_pay_type(
        self,
        user_id: int,
        year: int,
        month: int,
        pay_type_id: int,
    ) -> Optional[VariablePay]:
        return VariablePay.objects.filter(
            category=VariablePay.CATEGORY_INVOICE_PAYROLL,
            year=year,
            month=month,
            user_id=user_id,
            pay_type_id=pay_type_id,
        ).first()

    def list_by_user(
        self,
        user_id: int,
        year: int,
        month: int,
    ) -> BaseManager[VariablePay]:
        return VariablePay.objects.filter(
            category=VariablePay.CATEGORY_INVOICE_PAYROLL,
            year=year,
            month=month,
            user_id=user_id,
        ).all()

    def get_attachment(
        self,
        non_recurring_id: int,
    ) -> Optional[CompensationAttachment]:
        return CompensationAttachment.objects.filter(
            item_id=non_recurring_id,
            item_type=VariablePay.TYPE_COMPENSATION,
        ).first()

    def get_pay_type(
        self,
        pay_type_id: int,
    ) -> Optional[VariablePayType]:
        return VariablePayType.objects.filter(
            id=pay_type_id,
        ).first()

    def get_display_name(
        self,
        user_id: int,
        year: int,
        month: int,
        pay_type_id: int,
    ) -> str:
        pay_type = self.get_pay_type(pay_type_id)
        type_name = pay_type.type_name

        items = list(
            VariablePay.objects.filter(
                category=VariablePay.CATEGORY_INVOICE_PAYROLL,
                year=year,
                month=month,
                user_id=user_id,
                pay_type_id=pay_type_id,
            ).all()
        )

        if not items:
            return type_name

        # check if the type name already exists
        if not [item for item in items if item.display_name == type_name]:
            return type_name

        # some simply checking for name
        # if the display name already exists, we need to add a number to it
        num = len(items) + 1
        name = f"{type_name} ({num})"
        if [item for item in items if item.display_name == name]:
            name = f"{type_name} ({num + 1})"
        if [item for item in items if item.display_name == name]:
            name = f"{type_name} ({num + 1})"
        return name

    # endregion

    # region Save
    def add(
        self,
        user_id: int,
        year: int,
        month: int,
        pay_type_id: int,
        payment_currency: str,
        billing_currency: str,
        amount: Decimal,
        # optional
        note: str = "",
        client_note: str = "",
        attachment_id: Optional[int] = None,
        operator: Optional[User] = None,
    ) -> VariablePay:
        non_recurring = VariablePay(
            category=VariablePay.CATEGORY_INVOICE_PAYROLL,
            year=year,
            month=month,
            user_id=user_id,
            pay_type_id=pay_type_id,
            display_name=self.get_display_name(
                user_id=user_id,
                year=year,
                month=month,
                pay_type_id=pay_type_id,
            ),
            amount=amount,
            payment_currency=payment_currency,
            billing_currency=billing_currency,
            note=note or "",
            client_note=client_note or "",
            updated_on=timezone.now(),
        )
        non_recurring.save()

        # save attachment
        if attachment_id:
            self.save_attachment(
                non_recurring_id=non_recurring.id,
                attachment_id=attachment_id,
            )

        # save history
        if operator:
            self.add_submitted_history(
                non_recurring=non_recurring,
                operator=operator,
            )

        return non_recurring

    def update(
        self,
        non_recurring: VariablePay,
        year: int,
        month: int,
        pay_type_id: int,
        amount: Decimal,
        # optional
        note: Optional[str] = None,
        client_note: Optional[str] = None,
        attachment_id: Optional[int] = None,
        operator: Optional[User] = None,
        is_client_update: bool = False,
    ) -> VariablePay:
        # get previous info
        previous_info = self.__get_history_info_from_non_recurring(
            non_recurring
        )

        # save properties
        pay_type_changed = non_recurring.pay_type_id != pay_type_id
        year_month_changed = (
            non_recurring.year != year or non_recurring.month != month
        )
        non_recurring.year = year
        non_recurring.month = month
        non_recurring.pay_type_id = pay_type_id
        non_recurring.amount = amount

        if pay_type_changed or year_month_changed:
            non_recurring.display_name = self.get_display_name(
                user_id=non_recurring.user_id,
                year=year,
                month=month,
                pay_type_id=pay_type_id,
            )

        if is_client_update:
            non_recurring.client_note = (
                client_note or non_recurring.client_note
            )
        else:
            non_recurring.note = note or non_recurring.note
            attachment = self.get_attachment(non_recurring.id)
            # save attachment
            if attachment and attachment.id != attachment_id:
                self.delete_attachment(non_recurring.id)
            if attachment_id:
                self.save_attachment(
                    non_recurring_id=non_recurring.id,
                    attachment_id=attachment_id,
                )

        non_recurring.save()

        # save history
        if operator:
            self.add_updated_history(
                non_recurring=non_recurring,
                operator=operator,
                previous_info=previous_info,
            )

        return non_recurring

    def delete(
        self,
        non_recurring: VariablePay,
    ):
        non_recurring.delete()

    def save_attachment(
        self,
        non_recurring_id: int,
        attachment_id: int,
    ):
        attachment = CompensationAttachment.objects.filter(
            id=attachment_id,
            item_type=VariablePay.TYPE_COMPENSATION,
        ).first()
        if not attachment:
            return None
        if attachment.item_id == non_recurring_id:
            return attachment

        attachment.item_id = non_recurring_id
        attachment.save()
        return attachment

    def delete_attachment(
        self,
        non_recurring_id: int,
    ):
        CompensationAttachment.objects.filter(
            item_id=non_recurring_id,
            item_type=VariablePay.TYPE_COMPENSATION,
        ).delete()

    # endregion

    # region history
    def add_submitted_history(
        self,
        non_recurring: VariablePay,
        operator: User,
    ):
        if not operator or not non_recurring:
            return

        history = VariablePayHistory(
            action=VariablePayHistory.ACTION_SUMMITTED,
            variable_pay_id=non_recurring.id,
            updated_at=timezone.now(),
            operator_id=operator.id,
            info=self.__get_history_info_from_non_recurring(non_recurring),
        )
        history.save()
        return history

    def add_updated_history(
        self,
        non_recurring: VariablePay,
        operator: User,
        previous_info: dict,
    ):
        if not operator or not non_recurring:
            return

        cur_info = self.__get_history_info_from_non_recurring(non_recurring)
        updated_info = {
            k: v for k, v in cur_info.items() if v != previous_info.get(k)
        }
        if not updated_info:
            return

        history = VariablePayHistory(
            action=VariablePayHistory.ACTION_UPDATED,
            variable_pay_id=non_recurring.id,
            updated_at=timezone.now(),
            operator_id=operator.id,
            info=updated_info,
        )
        history.save()
        return history

    # region

    # region Private
    def __get_history_info_from_non_recurring(
        self,
        non_recurring: VariablePay,
    ) -> dict:
        pay_type = self.get_pay_type(non_recurring.pay_type_id)

        info = {
            "Invoice Month": non_recurring.invoice_month,
            "Type": pay_type.type_name if pay_type else "",
            "Amount": non_recurring.amount,
            "Note": non_recurring.note,
            "Client Note": non_recurring.client_note,
        }
        return {k: v for k, v in info.items() if v}

    # endregion
