from decimal import Decimal
from typing import Dict, List, Optional

from pydantic import BaseModel


# region Request
class QueryPayrollRegularizationListRequest(BaseModel):
    year: int
    month: int
    client_id: int
    billing_group_id: Optional[int] = None
    user_id: Optional[int] = None
    country_id: Optional[int] = None


# endregion


# region Data
class PayrollRegularizationItemInfo(BaseModel):
    category: int
    sub_category: str
    reference_type: str
    name: str
    actual: Optional[Decimal] = None
    projected: Optional[Decimal] = None
    diff: Decimal


class PayrollRegularizationResultInfo(BaseModel):
    recurring_income_diff: Decimal
    non_recurring_income_diff: Decimal
    expense_diff: Decimal
    burden_diff: Decimal
    diff_total: Decimal


class PayrollRegularizationActualProjectedInfo(BaseModel):
    recurring_income: Decimal
    non_recurring_income: Decimal
    expense: Decimal
    burden: Decimal


class PayrollRegularizationBillingInfo(PayrollRegularizationResultInfo):
    client_id: int
    year: int
    month: int
    payment_currency: str
    billing_currency: str
    billing_rate: Decimal


class PayrollRegularizationDetail(BaseModel):
    user_id: int
    client_id: int
    year: int
    month: int
    diff_payroll_year: int
    diff_payroll_month: int
    invoice_payroll_id: int
    actual_payroll_id: int
    payment_currency: str
    billing_currency: str
    # diff
    projected_total: Decimal
    actual_total: Decimal
    actual_result: PayrollRegularizationActualProjectedInfo
    projected_result: PayrollRegularizationActualProjectedInfo
    diff_total: Decimal
    diff_result: PayrollRegularizationResultInfo
    diff_items: List[PayrollRegularizationItemInfo] = []


# endregion


# region Response
class PayrollRegularizationListItemWithUserInfoResponse(BaseModel):
    user_id: int
    user_name: str
    payment_currency: str
    billing_group_id: int
    billing_group_name: str
    country_id: int
    country_name: str
    invoice_payroll_id: int
    prev_actual_payroll_id: int
    # diff
    diff_result: PayrollRegularizationResultInfo
    # billing
    diff_billing: PayrollRegularizationBillingInfo


class PayrollBillingRegularizationListWithUserInfoResponse(BaseModel):
    year: int
    month: int
    rows: List[PayrollRegularizationListItemWithUserInfoResponse] = []


class PayrollRegularizationUserBillingAndUSDInfoResponse(BaseModel):
    user_id: int
    year: int
    month: int
    # diff
    diff_result: Optional[PayrollRegularizationResultInfo] = None
    # billing
    diff_billing: Optional[PayrollRegularizationBillingInfo] = None
    # usd
    diff_usd: Optional[PayrollRegularizationBillingInfo] = None


class PayrollRegularizationUserDiffDetailsResponse(BaseModel):
    user_id: int
    year: int
    month: int
    payment_currency: str
    billing_currency: str
    projected_total: Decimal
    actual_total: Decimal
    actual_result: PayrollRegularizationActualProjectedInfo
    projected_result: PayrollRegularizationActualProjectedInfo
    diff_total: Decimal
    diff_result: PayrollRegularizationResultInfo
    diff_items: List[PayrollRegularizationItemInfo]
    result: List[Dict]


# endregion
