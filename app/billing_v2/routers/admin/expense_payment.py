from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from rest_framework.request import Request

from app.billing_v2.deps import get_expense_payment_service
from app.billing_v2.routers.base_views import BaseBillingV2AdminViewSet
from app.billing_v2.schemas.expense_payment import (
    RemoveExpensePaymentRequest,
    RemoveExpensePaymentResponse,
    SaveExpensePaymentResponse,
    UpdateExpensePaymentPayrollMonthRequest,
    CheckExpensePaymentRejectResponse,
)
from utils.constant import Permission
from utils.djangoplus.schemas import get_request_schema, get_response_schema
from utils.permission import permissions_checker
from app.constants import JunoPermissionEnum


class BillingV2ExpensePaymentAdminViewSet(BaseBillingV2AdminViewSet):
    name = "expense_payment"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.expense_payment_service = get_expense_payment_service()

    @swagger_auto_schema(
        operation_summary="Update payroll month",
        tags=["billing_v2"],
        request_body=get_request_schema(
            UpdateExpensePaymentPayrollMonthRequest
        ),
        responses={
            200: get_response_schema(SaveExpensePaymentResponse),
        },
    )
    @action(methods=["put"], url_path="payroll_month", detail=True)
    @permissions_checker(
        permissions=[
            Permission.EXPENSE_INTERNAL_APPROVAL,
            Permission.EXPENSE_CLIENT_APPROVAL,
            Permission.CLIENT_EXPENSE_APPROVAL,
            Permission.ADMIN_INVOICE_SPECIALCASE,
        ]
    )
    def update_payroll_month(self, request: Request, pk):
        req = self.parse_body(request, UpdateExpensePaymentPayrollMonthRequest)
        data = self.expense_payment_service.update_payroll_month(
            expense_report_id=pk,
            req=req,
            operator=request.user,
        )
        return self.response(data)

    @swagger_auto_schema(
        operation_summary="Check expense payment can be updated",
        tags=["billing_v2"],
        manual_parameters=[
            openapi.Parameter(
                "expense_report_id",
                openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
            ),
        ],
        responses={
            200: get_response_schema(CheckExpensePaymentRejectResponse),
        },
    )
    @action(methods=["get"], url_path="can_update", detail=False)
    @permissions_checker(
        permissions=[
            JunoPermissionEnum.ADMIN_EXPENSE_REJECT,
        ]
    )
    def check_expense_payment_can_be_updated(self, request: Request):
        expense_report_id = self.get_query_int(request, 'expense_report_id')
        data = self.expense_payment_service.check_expense_payment_can_be_updated(expense_report_id)
        return self.response(data)

    @swagger_auto_schema(
        operation_summary="Remove expense payments",
        tags=["billing_v2"],
        request_body=get_request_schema(RemoveExpensePaymentRequest),
        responses={
            200: get_response_schema(RemoveExpensePaymentResponse),
        },
    )
    @action(methods=["delete"], url_path="remove", detail=False)
    @permissions_checker(
        permissions=[
            JunoPermissionEnum.ADMIN_EXPENSE_REJECT,
        ]
    )
    def remove_expense_payments(self, request: Request):
        data = self.parse_body(request, RemoveExpensePaymentRequest)
        result = self.expense_payment_service.remove_expense_payments(expense_report_ids=data.expense_report_ids)
        return self.response(result)
