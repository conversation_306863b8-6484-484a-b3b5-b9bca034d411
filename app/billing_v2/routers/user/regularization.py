from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from rest_framework.request import Request

from app.billing_v2.deps import get_regularization_service
from app.billing_v2.routers.base_views import BaseBillingV2UserViewSet
from app.billing_v2.schemas.regularization import PayrollRegularizationDetail
from utils.constant import Permission as PERMISSION
from utils.djangoplus.schemas import get_response_schema
from utils.permission import permissions_checker


class BillingV2RegularizationUserViewSet(BaseBillingV2UserViewSet):
    name = "regularization"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.regularization_service = get_regularization_service()

    @swagger_auto_schema(
        operation_summary="Get user regularization",
        tags=["billing_v2"],
        manual_parameters=[
            openapi.Parameter(
                "user_id",
                openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
            ),
            openapi.Parameter(
                "year",
                openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
            ),
            openapi.Parameter(
                "month",
                openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
            ),
        ],
        responses={
            200: get_response_schema(PayrollRegularizationDetail),
        },
    )
    @action(
        methods=["get"],
        url_path="details",
        detail=False,
    )
    @permissions_checker(
        permissions=[
            PERMISSION.CLIENT_PAYREPORT_VIEW,
        ],
    )
    def get_user_regularization_details(self, request: Request):
        user_id = self.get_query_int(request, "user_id")
        year = self.get_query_int(request, "year")
        month = self.get_query_int(request, "month")
        use_billing_currency = self.get_query_bool(
            request, "use_billing_currency"
        )

        resp = (
            self.regularization_service.get_user_regularization_diff_details(
                user_id=user_id,
                year=year,
                month=month,
                use_billing_currency=use_billing_currency,
            )
        )
        return self.response(resp)
