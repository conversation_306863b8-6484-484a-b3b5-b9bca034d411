from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from rest_framework.request import Request

from app.billing_v2.routers.base_views import BaseBillingV2UserViewSet
from app.billing_v2.schemas.billing_report_csv import (
    ExportBillingReportsCSVRequest,
)
from app.billing_v2.schemas.billing_report_excel import (
    ExportBillingReportsExcelRequest,
)
from app.billing_v2.schemas.billing_report_retriever import (
    GetUserBillingReportsResponse,
)
from app.billing_v2.services.billing_report_csv import BillingReportCSVService
from app.billing_v2.services.billing_report_excel import (
    BillingReportExcelService,
)
from app.billing_v2.services.billing_report_retriever import (
    BillingReportRetrieverService,
)
from app.constants import JoinPermissionEnum
from utils.constant import Permission
from utils.constant import Permission as PERMISSION
from utils.djangoplus.schemas import get_request_schema
from utils.permission import permissions_checker


class BillingV2BillingReportUserViewSet(BaseBillingV2UserViewSet):
    name = "billing_report"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.billing_report_retriever_service = BillingReportRetrieverService()
        self.billing_report_csv_service = BillingReportCSVService()
        self.billing_report_excel_service = BillingReportExcelService()

    @swagger_auto_schema(
        operation_summary="List Payrolls",
        tags=["join", "payrolls"],
        manual_parameters=[
            openapi.Parameter(
                "billing_year",
                openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
            ),
            openapi.Parameter(
                "billing_month",
                openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=True,
            ),
        ],
    )
    @permissions_checker(
        permissions=[
            Permission.CLIENT_PAYREPORT_VIEW,
        ]
    )
    def list(self, request: Request):
        resp = (
            self.billing_report_retriever_service.get_client_billing_reports(
                client_id=request.user.team_id,
                billing_year=self.get_query_int(request, "billing_year"),
                billing_month=self.get_query_int(request, "billing_month"),
                use_usd_if_multiple_billing_currencies=True,
                include_billing_items=False,
                operator=request.user,
            )
        )
        return self.response(resp.dict(exclude_none=True))

    @swagger_auto_schema(
        operation_summary="Get user billing reports detail",
        tags=["billing_v2"],
        request_body=get_request_schema(GetUserBillingReportsResponse),
    )
    @action(
        methods=["post"],
        url_path="details",
        detail=False,
    )
    @permissions_checker(
        permissions=[
            Permission.CLIENT_PAYREPORT_VIEW,
        ],
    )
    def get_user_billing_reports(self, request: Request):
        req = self.parse_body(request, GetUserBillingReportsResponse)
        resp = self.billing_report_retriever_service.get_user_billing_reports(
            user_id=req.user_id,
            billing_dates=req.billing_dates,
            operator=request.user,
        )
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="Export user billing reports csv",
        tags=["billing_v2"],
        request_body=get_request_schema(ExportBillingReportsCSVRequest),
    )
    @action(
        methods=["post"],
        url_path="csv",
        detail=False,
    )
    @permissions_checker(
        permissions=[
            Permission.CLIENT_PAYREPORT_VIEW,
        ],
    )
    def export_monthly_reports_csv(self, request: Request):
        req = self.parse_body(request, ExportBillingReportsCSVRequest)
        resp = self.billing_report_csv_service.export_monthly_reports(
            client_id=req.client_id,
            billing_year=req.billing_year,
            billing_month=req.billing_month,
            operator=request.user,
        )
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="Export user billing reports excel",
        tags=["billing_v2"],
        request_body=get_request_schema(ExportBillingReportsExcelRequest),
    )
    @action(
        methods=["post"],
        url_path="excel",
        detail=False,
    )
    @permissions_checker(
        permissions=[
            PERMISSION.CONTRACT_INVOICE_VIEW,
            PERMISSION.TEAM_FULLACCESS,
            JoinPermissionEnum.PAY_PAYMENT_FULL_ACCESS,
        ]
    )
    def export_monthly_reports_excel(self, request: Request):
        req = self.parse_body(request, ExportBillingReportsExcelRequest)
        resp = self.billing_report_excel_service.export_monthly_reports(
            client_id=req.client_id,
            billing_year=req.billing_year,
            billing_month=req.billing_month,
        )
        return self.response(resp)
