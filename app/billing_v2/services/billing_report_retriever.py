from decimal import Decimal
from typing import Dict, List, Optional

from app.billing_v2.schemas.billing_report import UserBillingReportResponse
from app.billing_v2.services.billing_report_base import (
    BaseBillingReportService,
)
from pay.models import BillableItem
from user.models import User
from utils.constant import get_currency_for_symbol


class BillingReportRetrieverService(BaseBillingReportService):
    def __init__(self):
        super().__init__()

    def get_user_billing_reports(
        self,
        user_id: int,
        billing_dates: List[str],
        operator: Optional[User] = None,
    ):
        """
        billing_dates: ["2025-01", "2025-02", "2025-03]
        """
        results = []
        reports: Dict[str, Optional[UserBillingReportResponse]] = {}
        if not billing_dates:
            return results

        date_list = []
        for date in billing_dates:
            year, month = date.split("-")
            year = int(year)
            month = int(month)
            report = self.get_user_billing_report(
                user_id=user_id,
                billing_year=year,
                billing_month=month,
            )
            date_key = f"{year}-{month:02d}"
            date_list.append(date_key)
            reports[date_key] = report

        def get_total_amounts(amounts_dict_list):
            date_total = {}
            for date_key in date_list:
                for amounts_dict in amounts_dict_list:
                    for date_amount in amounts_dict.values():
                        local_amount = Decimal(
                            date_amount.get(date_key, {}).get(
                                "local_amount", 0
                            )
                        )
                        billing_amount = Decimal(
                            date_amount.get(date_key, {}).get(
                                "billing_amount", 0
                            )
                        )
                        if date_key not in date_total:
                            date_total[date_key] = {
                                "local_amount": Decimal(0),
                                "billing_amount": Decimal(0),
                            }
                        date_total[date_key]["local_amount"] += local_amount
                        date_total[date_key][
                            "billing_amount"
                        ] += billing_amount
            for date_key, amounts in date_total.items():
                date_total[date_key]["local_amount"] = str(
                    amounts["local_amount"]
                )
                date_total[date_key]["billing_amount"] = str(
                    amounts["billing_amount"]
                )
            return date_total

        def get_date_amounts(amounts_dict):
            new_dict = {}
            for date_key in date_list:
                if date_key in amounts_dict:
                    item_data = amounts_dict[date_key]
                    new_dict[date_key] = {
                        "local_amount": str(
                            item_data.get("local_amount", "0")
                        ),
                        "billing_amount": str(
                            item_data.get("billing_amount", "0")
                        ),
                    }
                else:
                    new_dict[date_key] = {
                        "local_amount": "0",
                        "billing_amount": "0",
                    }
            return new_dict

        def get_data(name_set, items_dict):
            if not name_set or not items_dict:
                return []
            data = []
            for name in name_set:
                amounts = items_dict.get(name)
                if amounts:
                    meta_info = amounts.get("meta")
                    item = dict(
                        name=name,
                        amounts=get_date_amounts(amounts),
                    )
                    if meta_info:
                        item["meta"] = meta_info
                    data.append(item)

            return sorted(
                data,
                key=self.__sort_data,
            )

        regularization_amounts = {}
        rate_amounts = {}
        recurring_name_set, recurring_items = set(), {}
        non_recurring_name_set, non_recurring_items = set(), {}
        expense_name_set, expense_items = set(), {}
        benefit_name_set, benefit_items = set(), {}
        er_burden_name_set, er_burden_items = set(), {}
        fc_burden_name_set, fc_burden_items = set(), {}
        category_mapping = {
            BillableItem.CATEGORIES.COMPENSATION: (
                recurring_items,
                recurring_name_set,
            ),
            BillableItem.CATEGORIES.ALLOWANCE: (
                recurring_items,
                recurring_name_set,
            ),
            BillableItem.CATEGORIES.ADDITIONAL_INCOME: (
                recurring_items,
                recurring_name_set,
            ),
            BillableItem.CATEGORIES.NON_RECURRING: (
                non_recurring_items,
                non_recurring_name_set,
            ),
            BillableItem.CATEGORIES.EXPENSE: (expense_items, expense_name_set),
            BillableItem.CATEGORIES.BENEFIT: (benefit_items, benefit_name_set),
            BillableItem.CATEGORIES.REGULARIZED_BURDEN: (
                er_burden_items,
                er_burden_name_set,
            ),
            BillableItem.CATEGORIES.NON_REGULARIZED_BURDEN: (
                er_burden_items,
                er_burden_name_set,
            ),
            BillableItem.CATEGORIES.MANDATORY_CHARGE: (
                fc_burden_items,
                fc_burden_name_set,
            ),
            BillableItem.CATEGORIES.OPTIONAL_CHARGE: (
                fc_burden_items,
                fc_burden_name_set,
            ),
        }
        for date_key, report in reports.items():
            if not report:
                continue

            regularization_amounts[date_key] = {}
            local_currency = report.payment_currency
            billing_currency = report.billing_currency
            billing_rate = report.billing_rate or Decimal(0)
            rate_amounts[date_key] = {
                "local_currency": local_currency,
                "billing_currency": billing_currency,
                "rate": str(billing_rate),
                "label": f"{get_currency_for_symbol(local_currency)}1.00 = {get_currency_for_symbol(billing_currency)}{billing_rate}",
            }
            for item in report.billing_items:
                item_category = item.item_category
                item_name = item.item_name
                item_local_amount = item.item_payment_amount
                item_billing_amount = item.item_billing_amount

                if not item_local_amount:
                    continue

                if item_category == BillableItem.CATEGORIES.REGULARIZATION:
                    re_amount = regularization_amounts[date_key].get(
                        "local_amount"
                    ) or Decimal(0)
                    re_b_amount = regularization_amounts[date_key].get(
                        "billing_amount"
                    ) or Decimal(0)
                    regularization_amounts[date_key]["local_amount"] = (
                        re_amount + item_local_amount
                    )
                    regularization_amounts[date_key]["billing_amount"] = (
                        re_b_amount + item_billing_amount
                    )
                    continue

                if item_category in category_mapping:
                    category_items, category_name_set = category_mapping[
                        item_category
                    ]
                    category_name_set.add(item_name)
                    category_items.setdefault(item_name, {})[date_key] = {
                        "local_amount": item_local_amount,
                        "billing_amount": item_billing_amount,
                    }
                    category_items.setdefault(item_name, {})["meta"] = {
                        "group_type": item.group_type,
                        "category": item_category,
                        "name": item_name,
                    }

        if recurring_name_set or non_recurring_name_set:
            item_data = dict(
                name="Income",
                amounts=get_total_amounts(
                    [
                        recurring_items,
                        non_recurring_items,
                    ]
                ),
                subs=[],
            )
            if recurring_name_set:
                name_set = self.__name_sort(recurring_name_set)
                subs = get_data(name_set, recurring_items)
                if len(subs) == 1:
                    item_data["subs"] = subs
                else:
                    item_data["subs"].append(
                        dict(
                            name="Recurring",
                            amounts=get_total_amounts([recurring_items]),
                            subs=subs,
                        )
                    )
            if non_recurring_name_set:
                name_set = self.__name_sort(non_recurring_name_set)
                subs = get_data(non_recurring_name_set, non_recurring_items)
                if len(subs) == 1:
                    item_data["subs"] = item_data["subs"] + subs
                else:
                    item_data["subs"].append(
                        dict(
                            name="Non-Recurring",
                            amounts=get_total_amounts([non_recurring_items]),
                            subs=subs,
                        )
                    )
            if item_data["subs"]:
                results.append(item_data)

        if expense_name_set:
            item_data = dict(
                name="Expense",
                amounts=get_total_amounts([expense_items]),
            )
            results.append(item_data)

        if er_burden_name_set:
            name_set = self.__name_sort(er_burden_name_set)
            item_data = dict(
                name="Employer Burden",
                amounts=get_total_amounts([er_burden_items]),
                subs=get_data(er_burden_name_set, er_burden_items),
            )
            results.append(item_data)

        if fc_burden_name_set:
            name_set = self.__name_sort(fc_burden_name_set)
            item_data = dict(
                name="Financial Charges",
                amounts=get_total_amounts([fc_burden_items]),
                subs=get_data(name_set, fc_burden_items),
            )
            results.append(item_data)

        if benefit_name_set:
            name_set = self.__name_sort(benefit_name_set)
            item_data = dict(
                name="Benefits",
                amounts=get_total_amounts([benefit_items]),
                subs=get_data(name_set, benefit_items),
            )
            results.append(item_data)

        if regularization_amounts:
            valid_re_values = [v for v in regularization_amounts.values() if v]
            if valid_re_values:
                results.append(
                    dict(
                        name="Regularization",
                        amounts=get_date_amounts(regularization_amounts),
                    )
                )
        if rate_amounts:
            results.append(
                dict(
                    name="Exchange Rate",
                    rates=rate_amounts,
                )
            )

        return dict(
            rows=results,
        )

    def __name_sort(self, name_set: set) -> List[str]:
        return sorted(
            name_set,
            key=lambda x: (
                0 if x == "Gross Income" else 1,
                x.lower(),
            ),
        )

    def __sort_data(self, data_item: Dict):
        if not data_item:
            return (1, 1, "")
        name = data_item.get("name")
        meta = data_item.get("meta")
        if not name:
            return (1, 1, "")
        if not meta:
            return (1, 1, name)

        group_type = meta.get("group_type", "")
        item_cat = meta.get("category", "")

        return (
            self.VALUE_GROUP_DISPLAY_ORDERING.get(group_type, 1),
            self.VALUE_DISPLAY_ORDERING.get(item_cat, 1),
            name,
        )
