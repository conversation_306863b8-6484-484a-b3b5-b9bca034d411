# Generated by Django 3.1.13 on 2023-09-12 07:45

import jsonfield.fields
from django.db import migrations, models

import utils.basefield
import utils.djangoplus.aws_s3_storage
import utils.utils


class Migration(migrations.Migration):

    dependencies = [
        (
            "app",
            "0045_auto_20230913_0838",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="Event",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("has_delete", models.BooleanField(default=False)),
                (
                    "id",
                    models.CharField(
                        max_length=255, primary_key=True, serialize=False
                    ),
                ),
                (
                    "steam_key",
                    models.CharField(db_index=True, max_length=255, null=True),
                ),
                (
                    "event_type",
                    models.Char<PERSON>ield(db_index=True, max_length=255),
                ),
                ("payload", jsonfield.fields.J<PERSON><PERSON><PERSON>(default="")),
                (
                    "group_name",
                    models.Char<PERSON>ield(db_index=True, max_length=255, null=True),
                ),
                (
                    "consumer_name",
                    models.<PERSON>r<PERSON><PERSON>(db_index=True, max_length=255, null=True),
                ),
                ("errors", models.Text<PERSON>ield(blank=True)),
                (
                    "status",
                    models.CharField(db_index=True, max_length=255, null=True),
                ),
            ],
            options={
                "db_table": "event",
            },
        ),
        migrations.AlterField(
            model_name="document",
            name="file",
            field=utils.basefield.FileField(
                default="",
                storage=utils.djangoplus.aws_s3_storage.AwsS3DocStorage,
                upload_to=utils.utils.gen_file_path,
            ),
        ),
    ]
