# Generated by Django 3.1 on 2023-09-19 01:50

from django.db import migrations

from user.models import Permission, Role, RolePermission


def update_role_country_pedia_full_access_permission(apps, _):
    code = "Admin.CountryPedia.FullAccess"
    new_code = "Admin.CountryPedia.Edit"
    p = Permission.objects.filter(code=code).first()
    new_p = Permission.objects.filter(code=new_code).first()
    if p and new_p:
        RolePermission.objects.filter(code=code, permission=p).update(
            code=new_code, permission=new_p
        )


class Migration(migrations.Migration):
    dependencies = [
        (
            "app",
            "0060_auto_20231213_0352",
        ),
    ]

    operations = [
        migrations.RunPython(
            update_role_country_pedia_full_access_permission,
            reverse_code=migrations.RunPython.noop,
        )
    ]
