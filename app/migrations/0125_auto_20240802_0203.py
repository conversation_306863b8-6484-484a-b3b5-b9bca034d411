# Generated by Django 3.1 on 2024-08-02 02:03

from django.db import migrations


def add_timesheet_permission(apps, _):
    from app.auth.constants import Join<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>num
    from app.auth.utils import get_join_role
    from app.constants import Jo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    from user.models import Permission, RolePermission

    ts_permission = dict(
        code=JoinPermissionEnum.JOIN_CLIENT_TIMESHEET_SETTINGS_FULLACCESS,
        description="Join manage timesheet permission",
        scope=Permission.SCOPE_JOIN,
        category="Join",
    )
    p = Permission.objects.filter(code=ts_permission["code"]).first()
    if not p:
        # create permissions
        p = Permission(**ts_permission)
        p.save()

    p_map = dict()
    p_map[p.code] = p.id

    # create rle
    admin_role_id = get_join_role(JoinDefaultRoleEnum.Admin)
    if admin_role_id:
        drp = RolePermission(
            role_id=admin_role_id,
            code=p.code,
            permission_id=p.id,
        )
        drp.save()

    manager_role_id = get_join_role(JoinDefaultRoleEnum.Manager)
    if manager_role_id:
        mrp = RolePermission(
            role_id=manager_role_id,
            code=p.code,
            permission_id=p.id,
        )
        mrp.save()


class Migration(migrations.Migration):

    dependencies = [
        ("app", "0124_auto_20240725_0725"),
    ]

    operations = [
        migrations.RunPython(
            add_timesheet_permission,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
