# Generated by Django 3.1.13 on 2023-12-04 05:54

from django.db import migrations

import utils.basefield
import utils.djangoplus.aws_s3_storage
import utils.utils
from app.constants import JunoPermissionEnum
from user.models import Permission, Role, RolePermission
from utils.constant import ROLE
from utils.constant import Permission as PermissionConstant


# add join home admin permission
def add_admin_outsourced_payslip_permission(apps, _):

    # Admin.Outsourced.Payslip
    features_permission = dict(
        code=PermissionConstant.ADMIN_OUTSOURCED_PAYSLIP,
        description="Admin.Outsourced.Payslip",
        scope=Permission.SCOPE_ADMIN,
        category="Horizons",
    )
    if not Permission.objects.filter(
        code=features_permission["code"]
    ).exists():
        # create permissions
        features_permission = Permission(**features_permission)
        features_permission.save()


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0056_auto_20231121_0914"),
    ]

    operations = [
        migrations.RunPython(
            add_admin_outsourced_payslip_permission,
            reverse_code=migrations.RunPython.noop,
        )
    ]
