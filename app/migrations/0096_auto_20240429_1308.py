# Generated by Django 3.1.13 on 2024-04-29 13:08

from django.db import migrations

from user.models import Permission
from app.constants import Join<PERSON><PERSON><PERSON><PERSON><PERSON>


def add_permissions(apps, _):
    permission = Permission.objects.filter(
        code=JoinPermissionEnum.JOIN_CLIENT_CONTRACTOR_PAYMENTS_FULLACCESS
    ).first()
    if permission:
        return
    permission = Permission(
        code=JoinPermissionEnum.JOIN_CLIENT_CONTRACTOR_PAYMENTS_FULLACCESS,
        scope=Permission.SCOPE_JOIN,
        category="Join",
        name=JoinPermissionEnum.JOIN_CLIENT_CONTRACTOR_PAYMENTS_FULLACCESS,
    )
    permission.save()


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0095_auto_20240425_0221'),
    ]

    operations = [
        migrations.RunPython(
            add_permissions,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
