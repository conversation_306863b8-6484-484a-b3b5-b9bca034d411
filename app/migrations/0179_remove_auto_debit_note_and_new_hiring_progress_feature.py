# Generated by Django 3.1.13 on 2025-07-04 11:01

from django.db import migrations, models

def remove_features(apps, _):
    from auditlog.registry import auditlog

    from app.feature.models.feature import Feature

    auditlog.unregister(Feature)
    Feature.objects.filter(
        key__in=[
            "AUTO_DEBITNOTE_WITH_PREREGISTRATION",
            "NEW_HIRING_PROGRESS_PAGE"
        ]
    ).delete()

class Migration(migrations.Migration):

    dependencies = [
        ('app', '0178_auto_20250703_0255'),
    ]

    operations = [
        migrations.RunPython(remove_features, reverse_code=migrations.RunPython.noop),
    ]
