# Generated by Django 3.1.13 on 2023-12-04 05:54

from django.db import migrations

import utils.basefield
import utils.djangoplus.aws_s3_storage
import utils.utils
from app.constants import JunoPermissionEnum
from user.models import Permission, Role, RolePermission
from utils.constant import ROLE
from utils.constant import Permission as PermissionConstant


# add join home admin permission
def add_permissions(apps, _):
    permission = Permission.objects.filter(
        code=PermissionConstant.ADMIN_COUNTRY_EDIT
    ).first()
    if permission:
        return
    permission = Permission(
        code="Admin.CountryPedia.Edit",
        scope=Permission.SCOPE_ADMIN,
        category="CountryPedia",
        name="Admin.CountryPedia.Edit",
    )
    permission.save()


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0058_auto_20231207_0554"),
    ]

    operations = [
        migrations.RunPython(
            add_permissions,
            reverse_code=migrations.RunPython.noop,
        )
    ]
