# Generated by Django 3.1.13 on 2024-04-02 01:18

from django.db import migrations

from app.constants import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from user.models import Permission


def add_permissions(apps, _):
    permission = Permission.objects.filter(
        code=JoinPermissionEnum.CONTRACTOR_MYPAYMENTS_FULL_ACCESS
    ).first()
    if permission:
        return
    permission = Permission(
        code=JoinPermissionEnum.CONTRACTOR_MYPAYMENTS_FULL_ACCESS,
        scope=Permission.SCOPE_JOIN,
        category="Join",
        name=JoinPermissionEnum.CONTRACTOR_MYPAYMENTS_FULL_ACCESS,
    )
    permission.save()


class Migration(migrations.Migration):

    dependencies = [
        ("app", "0082_auto_20240328_1001"),
    ]

    operations = [
        migrations.RunPython(
            add_permissions,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
