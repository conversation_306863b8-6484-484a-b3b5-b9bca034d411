# Generated by Django 3.1.13 on 2024-03-13 02:04

from django.db import migrations, models
import utils.utils


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0077_auto_20240312_0702'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContractorPayout',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('has_delete', models.BooleanField(default=False)),
                ('invoice_id', models.IntegerField(null=True)),
                ('user_id', models.IntegerField(db_index=True)),
                ('client_id', models.IntegerField(db_index=True)),
                ('billing_group_id', models.IntegerField()),
                ('payment_proof_id', models.IntegerField(null=True)),
                ('reference', models.CharField(max_length=100)),
                ('status', models.CharField(choices=[('Open', 'Open'), ('Locked', 'Locked'), ('Pending Payment', 'Pending Payment'), ('Payment Submitted', 'Payment Submitted'), ('Paid', 'Paid')], default='Open', max_length=20)),
                ('currency', models.CharField(db_index=True, max_length=6)),
                ('billing_currency', models.CharField(db_index=True, max_length=6)),
                ('year', models.PositiveSmallIntegerField(db_index=True)),
                ('month', models.PositiveSmallIntegerField(choices=[(1, 'Jan.'), (2, 'Feb.'), (3, 'Mar.'), (4, 'Apr.'), (5, 'May.'), (6, 'Jun.'), (7, 'Jul.'), (8, 'Aug.'), (9, 'Sept.'), (10, 'Oct.'), (11, 'Nov.'), (12, 'Dec.')], db_index=True)),
            ],
            options={
                'db_table': 'pay_contractor_payout',
            },
        ),
        migrations.CreateModel(
            name='ContractorPayoutFee',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('has_delete', models.BooleanField(default=False)),
                ('user_id', models.IntegerField(db_index=True)),
                ('client_id', models.IntegerField(db_index=True)),
                ('billing_group_id', models.IntegerField()),
                ('invoice_id', models.IntegerField(null=True)),
                ('payout_id', models.IntegerField()),
                ('fee_setting_id', models.IntegerField(null=True)),
                ('status', models.CharField(choices=[('Open', 'Open'), ('Locked', 'Locked'), ('Pending Payment', 'Pending Payment'), ('Payment Submitted', 'Payment Submitted'), ('Paid', 'Paid')], default='Open', max_length=20)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=20)),
                ('billing_currency', models.CharField(db_index=True, max_length=6)),
                ('year', models.PositiveSmallIntegerField(db_index=True)),
                ('month', models.PositiveSmallIntegerField(choices=[(1, 'Jan.'), (2, 'Feb.'), (3, 'Mar.'), (4, 'Apr.'), (5, 'May.'), (6, 'Jun.'), (7, 'Jul.'), (8, 'Aug.'), (9, 'Sept.'), (10, 'Oct.'), (11, 'Nov.'), (12, 'Dec.')], db_index=True)),
            ],
            options={
                'db_table': 'pay_contractor_payout_fee',
            },
        ),
        migrations.CreateModel(
            name='ContractorPayoutItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('has_delete', models.BooleanField(default=False)),
                ('payout_id', models.IntegerField()),
                ('type', models.CharField(choices=[('RECURRING', 'Recurring'), ('NON_RECURRING', 'Non Recurring'), ('EXPENSE', 'Expense')], max_length=20)),
                ('reference_id', models.IntegerField()),
                ('sub_type', models.CharField(choices=[('MONTHLY', 'Monthly Recurring'), ('DAILY', 'Daily Rate'), ('HOURLY', 'Hourly Rate'), ('EXPENSE', 'Expense'), ('MILESTONE', 'Milestone')], max_length=20)),
                ('name', models.CharField(max_length=100)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=20)),
            ],
            options={
                'db_table': 'pay_contractor_payout_item',
            },
        ),
        migrations.AddIndex(
            model_name='contractorpayout',
            index=models.Index(fields=['year', 'month'], name='pay_contrac_year_dc856b_idx'),
        ),
        migrations.AddIndex(
            model_name='contractorpayout',
            index=models.Index(fields=['status'], name='pay_contrac_status_632567_idx'),
        ),
    ]
