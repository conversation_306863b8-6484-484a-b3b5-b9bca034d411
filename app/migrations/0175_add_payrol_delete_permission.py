from django.db import migrations

from app.constants import JunoPermission<PERSON>num
from user.models import Permission as ModelPermission


def add_permissions(apps, schema_editor):
    permission_code = JunoPermissionEnum.JUNO_PAYROLL_DELETE_ACCESS
    permission = ModelPermission.objects.filter(code=permission_code).first()
    if not permission:
        ModelPermission.objects.create(
            code=permission_code,
            scope=ModelPermission.SCOPE_ADMIN,
            category="Juno",
            name=permission_code,
        )


class Migration(migrations.Migration):
    dependencies = [
        ("app", "0174_add_feature_flag_new_direct_contractor"),
    ]

    operations = [
        migrations.RunPython(
            add_permissions, reverse_code=migrations.RunPython.noop
        ),
    ]
