from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from app.constants import Join<PERSON>er<PERSON>Enum
from app.contractor.routers.contractor import ContractorViewSet
from app.contractor.schemas.payout_fee import (
    PayoutFeeResponse,
    QueryPayoutFeeRequest,
)
from app.contractor.services import contractor_service
from utils.constant import Permission
from utils.djangoplus.schemas import get_request_schema, get_response_schema
from utils.permission import permissions_checker


class ContractorPayoutFeeViewSet(ContractorViewSet):
    @swagger_auto_schema(
        operation_summary="Get Contractor payout fees by user id",
        tags=["juno", "contractor"],
        manual_parameters=[
            openapi.Parameter(
                "status",
                openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "year",
                openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=False,
            ),
            openapi.Parameter(
                "month",
                openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                required=False,
            ),
        ],
        responses={200: get_response_schema(PayoutFeeResponse, page=True)},
    )
    @permissions_checker(
        permissions=[
            Permission.ADMIN_CLIENTUSER_VIEW,
        ]
    )
    def list(
        self,
        request,
        user_id=None,
    ):
        page = self.get_pagination_query(request)
        data = QueryPayoutFeeRequest(
            user_id=user_id,
            status=request.query_params.get("status"),
            year=request.query_params.get("year"),
            month=request.query_params.get("month"),
        )
        resp = contractor_service.list_payout_fees(request.user, data, page)
        return self.response(resp)
