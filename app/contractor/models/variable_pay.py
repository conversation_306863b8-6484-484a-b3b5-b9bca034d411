from django.db import models
from auditlog.registry import auditlog

from utils.basemodel import BaseModel


class VariablePay(BaseModel):
    user = models.ForeignKey(
        "user.User",
        on_delete=models.CASCADE,
        related_name="contactor_variable_paies",
    )
    year = models.PositiveSmallIntegerField()
    month = models.PositiveSmallIntegerField()
    pay_type = models.CharField(max_length=32, default="BONUS")
    payment_currency = models.CharField(max_length=6, db_index=True)
    billing_currency = models.CharField(max_length=6, db_index=True)
    amount = models.DecimalField(max_digits=20, decimal_places=2, default=0)
    converted_amount = models.DecimalField(
        max_digits=20, decimal_places=2, default=0
    )
    note = models.CharField(max_length=100, default="")
    document_id = models.IntegerField(default=0, null=True)

    class Meta:
        db_table = "contractor_variable_pay"
        index_together = ["user_id", "year", "month", "pay_type"]


auditlog.register(VariablePay, exclude_fields=["created_at", "updated_at"])
