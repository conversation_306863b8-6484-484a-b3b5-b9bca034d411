from django.db import models


class CostSimulatorClientLog(models.Model):
    client_id = models.IntegerField(null=False)
    user_id = models.IntegerField(null=False)
    country_id = models.IntegerField(null=True)
    annual_gross_income = models.DecimalField(
        max_digits=14, decimal_places=2, null=True, blank=True
    )
    billing_currency = models.CharField(max_length=6, default="")
    created_at = models.DateTimeField(auto_now_add=True)
    work_permit_requirement = models.BooleanField(null=True)
    region_id = models.IntegerField(null=True)
    currency = models.CharField(max_length=6, default=None)

    class Meta:
        db_table = "cost_simulator_client_log"
