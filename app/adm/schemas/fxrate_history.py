from typing import Any, List, Optional

from pydantic import BaseModel

from pay.models import FXRateHistory


class FXRateHistoryItem(BaseModel):
    id: int
    action: str
    operator_type: str
    operator_name: str
    operator_id: int
    created_at: str
    payload: Any
    year: int
    month: int
    file: Optional[str]
    description: str

    @classmethod
    def from_model(cls, m: FXRateHistory):
        return cls(
            id=m.id,
            action=m.action,
            operator_type=m.operator_type,
            operator_name=m.operator_name,
            operator_id=m.operator_id,
            created_at=m.created_at.strftime("%Y-%m-%d %H:%M"),
            payload=m.payload,
            year=m.year,
            month=m.month,
            file=m.file.url if m.file else None,
            description=m.description,
        )


class FXRateHistoryGroupResponse(BaseModel):
    time: str
    operator_name: str
    action: str
    history_set: List[FXRateHistoryItem] = []
