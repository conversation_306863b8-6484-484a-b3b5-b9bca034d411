from rest_framework.decorators import action
from rest_framework.request import Request
from rest_framework_simplejwt.authentication import JWTAuthentication

from app.pay.schemas.variable_pay_deadline import QueryRequest
from app.pay.services import variable_pay_deadline_service
from utils.authentications import A<PERSON><PERSON>ey<PERSON>uthentication
from utils.djangoplus.views import UserViewSet


class VariablePayDeadlineViewSet(UserViewSet):
    name = "variable_pay_deadline"

    @action(
        methods=["get"],
        url_path="deadline",
        detail=False,
        authentication_classes=[JWTAuthentication, ApiKeyAuthentication],
        permission_classes=[],
    )
    def get_deadline(self, request: Request):
        input = QueryRequest(
            provider_id=self.get_query_int(request, "provider_id"),
            year=self.get_query_int(request, "year"),
            month=self.get_query_int(request, "month"),
        )
        resp = variable_pay_deadline_service.get_deadline(input)
        return self.response(resp)
