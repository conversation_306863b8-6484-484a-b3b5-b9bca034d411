from rest_framework.decorators import action
from rest_framework.request import Request

from app.pay.schemas.benefits import UpdateUserBenefitsDependantsRequest
from app.pay.services import import_benefits_service, user_benefits_service
from utils.djangoplus.views import InternalViewSet


class UserBenefitsInternalViewSet(InternalViewSet):
    name = "internal/user-benefits"

    @action(methods=["post"], url_path="import-config", detail=False)
    def import_config(self, request: Request):
        country_id = request.data.get("country_id")
        file = request.data.get("file")

        assert country_id, "mandatory parameters: country_id"
        assert file, "mandatory parameters: file"

        resp = import_benefits_service.import_config(
            country_id=country_id,
            csv_file=file,
        )
        return self.response(resp)

    @action(methods=["post"], url_path="import-user-values", detail=False)
    def import_benefits_with_csv(self, request: Request):
        file = request.data.get("file")

        assert file, "mandatory parameters: file"

        resp = import_benefits_service.import_user_values(
            csv_file=file,
        )
        return self.response(resp)

    @action(
        methods=["post"],
        url_path="import-user-dependants-values",
        detail=False,
    )
    def import_dependants_benefits_with_csv(self, request: Request):
        file = request.data.get("file")

        assert file, "mandatory parameters: file"

        resp = import_benefits_service.import_user_depandents_values(
            csv_file=file,
        )
        return self.response(resp)

    @action(methods=["put"], url_path="dependants", detail=False)
    def update_dependants(self, request: Request):
        data = self.parse_body(request, UpdateUserBenefitsDependantsRequest)
        resp = user_benefits_service.refresh_benefits_dependants(
            user_id=data.user_id,
            dependants=data.dependants,
            operator_id=data.operator_id,
        )
        return self.response(resp)
