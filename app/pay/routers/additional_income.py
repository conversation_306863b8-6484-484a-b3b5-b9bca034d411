from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from app.constants import JoinPermissionEnum
from app.pay.schemas.additional_income import (
    SaveUserAdditionalIncomeListRequest,
    UserAdditionalIncomeDetailListResponse,
    UserAdditionalIncomeListResponse,
)
from app.pay.services import additional_income_service
from utils.constant import Permission
from utils.djangoplus.schemas import (
    get_request_schema,
    get_response_schema,
    pagination_parameters,
)
from utils.djangoplus.views import UserViewSet
from utils.permission import permissions_checker


class UserAdditionalIncomeViewSet(UserViewSet):
    name = "user-additional-income"

    @swagger_auto_schema(
        operation_summary="Client get User active additional income",
        tags=["join", "compensation"],
        manual_parameters=pagination_parameters(
            [
                openapi.Parameter(
                    "employee_id", openapi.IN_QUERY, type=openapi.TYPE_INTEGER
                ),
            ]
        ),
        responses={
            200: get_response_schema(UserAdditionalIncomeDetailListResponse)
        },
    )
    @permissions_checker(
        permissions=[
            JoinPermissionEnum.EMPLOYEE_PROFILE_FULL_ACCESS,
            JoinPermissionEnum.EMPLOYEE_SELF_PROFILE_FULL_ACCESS,
        ]
    )
    @action(methods=["get"], url_path="active", detail=False)
    def active_additional_income_client(self, request):
        employee_id = self.get_query_int(request, "employee_id")
        incomes = additional_income_service.active_and_upcoming_version_income_from_join(
            user=request.user,
            employee_id=employee_id,
        )
        return self.response(
            UserAdditionalIncomeDetailListResponse(
                incomes=incomes,
            )
        )
