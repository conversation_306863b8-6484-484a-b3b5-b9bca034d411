from datetime import date

from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from rest_framework.request import Request

from app.billing_v2.schemas.expense_payment import (
    SaveExpensePaymentRequest as V2SaveExpensePaymentRequest,
)
from app.pay.schemas.expense_payment import (
    CreateExpensePaymentRequest,
    ExpensePaymentClaimResponse,
)
from app.pay.services import expense_payment_service
from utils.authentications import ApiKeyAuthentication
from utils.constant import Permission
from utils.djangoplus.schemas import get_request_schema, get_response_schema
from utils.djangoplus.views import UserViewSet
from utils.permission import permissions_checker


class ExpensePaymentViewSet(UserViewSet):

    name = "pay"

    @swagger_auto_schema(
        operation_summary="create expense payment record",
        tags=["join", "expense"],
        request_body=get_request_schema(CreateExpensePaymentRequest),
        responses={200: get_response_schema(ExpensePaymentClaimResponse)},
    )
    @action(
        methods=["post"],
        url_path="expense_payment_claim",
        detail=False,
        authentication_classes=[ApiKeyAuthentication],
        permission_classes=[],
    )
    def create_expense_payment(self, request):
        from app.billing_v2.deps import get_expense_payment_service

        data = self.parse_body(request, CreateExpensePaymentRequest)

        expense_payment_service = get_expense_payment_service()
        _, expense_payment = expense_payment_service.add_expense_payment(
            req=V2SaveExpensePaymentRequest(
                user_id=data.user_id,
                expense_report_id=data.expense_report_id,
                amount=data.amount,
                name=data.name,
            ),
            operator_id=data.operator_id,
        )
        if not expense_payment:
            raise Exception("Failed to create expense payment")

        payroll_month = date(
            year=expense_payment.year,
            month=expense_payment.month,
            day=1,
        )
        resp = ExpensePaymentClaimResponse(
            user_id=expense_payment.user_id,
            expense_report_id=expense_payment.expense_report_id,
            payroll_month=payroll_month.strftime("%Y-%m"),
        )
        return self.response(resp)

    @swagger_auto_schema(
        operation_summary="Check if user can edit expense invoice month",
        tags=["join", "expense"],
    )
    @action(
        methods=["get"],
        url_path="expense/check",
        detail=False,
    )
    @permissions_checker(
        permissions=[
            Permission.EXPENSE_INTERNAL_APPROVAL,
            Permission.EXPENSE_CLIENT_APPROVAL,
            Permission.CLIENT_EXPENSE_APPROVAL,
            Permission.ADMIN_INVOICE_SPECIALCASE,
        ]
    )
    def check_edit_expense_month(self, request: Request):
        user = request.user
        resp = expense_payment_service.check_can_edit_expense_month(user)
        return self.response(resp)
