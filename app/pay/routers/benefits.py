from typing import List

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from app.auth.constants import Join<PERSON><PERSON><PERSON>RoleEnum
from app.auth.utils import get_join_role
from app.constants import Join<PERSON><PERSON><PERSON><PERSON><PERSON>
from app.pay.schemas.benefits import (
    UserBenefitsDetailListResponse,
    UserBenefitsDetailResponse,
)
from app.pay.services import user_benefits_service
from utils.djangoplus.schemas import get_response_schema, pagination_parameters
from utils.djangoplus.views import UserViewSet
from utils.permission import permissions_checker


class UserBenefitsViewSet(UserViewSet):
    name = "user-benefits"

    @swagger_auto_schema(
        operation_summary="Client get User active benefits",
        tags=["join", "compensation"],
        manual_parameters=pagination_parameters(
            [
                openapi.Parameter(
                    "employee_id", openapi.IN_QUERY, type=openapi.TYPE_INTEGER
                ),
            ]
        ),
        responses={200: get_response_schema(UserBenefitsDetailListResponse)},
    )
    @permissions_checker(
        permissions=[
            JoinPermissionEnum.EMPLOYEE_PROFILE_FULL_ACCESS,
            JoinPermissionEnum.EMPLOYEE_SELF_PROFILE_FULL_ACCESS,
        ]
    )
    @action(methods=["get"], url_path="active", detail=False)
    def active_benefits(self, request):
        employee_id = self.get_query_int(request, "employee_id")
        user_benefits = (
            user_benefits_service.active_and_upcoming_version_benefits(
                user_id=employee_id,
                serialize=True,
            )
        )
        if self._is_employee(request):
            user_benefits = self._filter_user_benefits(user_benefits)
        return self.response(
            UserBenefitsDetailListResponse(list=user_benefits)
        )

    def _is_employee(self, request) -> bool:
        return request.user.role_id == get_join_role(
            JoinDefaultRoleEnum.Employee
        )

    def _filter_user_benefits(
        self, user_benefits: List[UserBenefitsDetailResponse]
    ) -> List[UserBenefitsDetailResponse]:
        for user_benefit in user_benefits:
            user_benefit.items = [
                item
                for item in user_benefit.items
                if item.cell_value is not None and item.cell_value > 0
            ]
        return user_benefits
