from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.request import Request

from app.pay.schemas.allowance_type import (
    AllowanceTypeResponse,
    QueryAllowanceTypeRequest,
)
from app.pay.services import allowance_type_service
from utils.djangoplus.schemas import get_response_schema, pagination_parameters
from utils.djangoplus.views import UserViewSet


class AllowanceTypeViewSet(UserViewSet):

    name = "allowance_type"

    @swagger_auto_schema(
        operation_summary="List allowance",
        tags=["join", "compensation"],
        manual_parameters=pagination_parameters(
            [
                openapi.Parameter(
                    "country",
                    openapi.IN_QUERY,
                    type=openapi.TYPE_INTEGER,
                    required=True,
                ),
                openapi.Parameter(
                    "name", openapi.IN_QUERY, type=openapi.TYPE_STRING
                ),
                openapi.Parameter(
                    "ids",
                    openapi.IN_QUERY,
                    type=openapi.TYPE_STRING,
                    description="1,2,3",
                ),
            ]
        ),
        responses={200: get_response_schema(AllowanceTypeResponse, page=True)},
    )
    def list(self, request: Request):
        page = self.get_pagination_query(request)
        query = QueryAllowanceTypeRequest(
            country=self.get_query_int(request, "country"),
            name=request.query_params.get("name"),
            ids=self.get_ids_query_string(request),
        )
        resp = allowance_type_service.list(query, page)
        return self.response(resp)
