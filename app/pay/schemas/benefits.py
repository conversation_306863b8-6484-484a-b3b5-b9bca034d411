from datetime import date
from decimal import Decimal
from typing import List, Optional

from pydantic import BaseModel

from adm.models import (
    CountryBenefit,
    CountryBenefitCell,
    CountryBenefitPackage,
    CountryBenefitRange,
)
from pay.models import (
    Compensation,
    UserBenefits,
    UserBenefitsDependant,
    UserBenefitsItem,
)


class UserBenefitDependantRequest(BaseModel):
    name: str
    date_of_birth: date
    relationship: str


class UserBenefitItemRequest(BaseModel):
    benefit_type: int
    package_id: int
    package_name: Optional[str] = None
    num_of_dependants: Optional[int] = None
    dependant: Optional[UserBenefitDependantRequest] = None


class SaveUserBenefitsRequest(BaseModel):
    id: Optional[int] = None
    effective_from: date
    effective_to: Optional[date]
    items: List[UserBenefitItemRequest]


class ImportSaveUserBenefitsRequest(BaseModel):
    user_id: int
    data: SaveUserBenefitsRequest


class SaveUserBenefitsListRequest(BaseModel):
    user_id: int
    list: List[SaveUserBenefitsRequest] = []


class CalculateUserBenefitItemData:
    def __init__(
        self,
        benefit_type: CountryBenefit,
        package: CountryBenefitPackage,
        range: CountryBenefitRange,
        cell: CountryBenefitCell,
        value: Decimal,
        age: int,
        date_of_birth: Optional[date] = None,
        gross_income: Optional[Compensation] = None,
        monthly_salary: Optional[Decimal] = None,
    ):
        self.benefit_type = benefit_type
        self.package = package
        self.range = range
        self.cell = cell
        self.value = value
        self.age = age
        self.date_of_birth = date_of_birth
        self.gross_income = gross_income
        self.monthly_salary = monthly_salary or Decimal(0)


class UserBenefitsItemDetailResponse(BaseModel):
    id: int
    user_id: int
    benefits_id: int
    # benefit type
    benefit_type_id: int
    benefit_type_name: str
    benefit_type_country_id: int
    benefit_type_mandatory: bool
    benefit_type_link: str
    benefit_type_link_desc: str
    benefit_type_coverage_for: str
    benefit_type_calc_type: str
    benefit_type_rate_base: Optional[str] = None
    benefit_type_range_reference: Optional[str] = None
    # package & range & cell
    package_id: int
    package_name: str
    range_id: Optional[int] = None
    range_value: Optional[Decimal] = None
    cell_id: Optional[int] = None
    cell_value: Optional[Decimal] = None
    # dependant
    dependant_name: Optional[str] = None
    dependant_date_of_birth: Optional[date] = None
    dependant_relationship: Optional[str] = None
    # value
    gross_income_id: Optional[int] = None
    gross_income_annual_salary: Optional[Decimal] = None
    monthly_salary: Optional[Decimal] = None
    age: int
    date_of_birth: Optional[date] = None
    num_of_dependants: int
    range_reference_desc: str = ""
    coverage_for_desc: str = ""
    monthly_benefits: str = ""
    value: Decimal

    @classmethod
    def from_model(
        cls,
        ub: UserBenefits,
        ubi: UserBenefitsItem,
        package: Optional[CountryBenefitPackage] = None,
    ):
        from app.pay.services import user_benefits_service

        if not package:
            package = CountryBenefitPackage.objects.filter(
                benefit_id=ubi.benefit_type_id,
                name=ubi.package,
            ).first()

        benefit_type: CountryBenefit = ubi.benefit_type
        type_coverage_for = benefit_type.coverage_for
        type_reference = benefit_type.range_reference
        type_calc_type = benefit_type.calc_type
        type_rate_base = benefit_type.rate_base
        range_id = None
        range_value = None
        cell_id = None
        cell_value = None
        gross_income_id = None
        gross_income_annual_salary = None
        monthly_salary = None
        country = benefit_type.country
        currency = country.currency
        coverage_for_desc = ""
        range_reference_desc = ""
        monthly_benefits = ""
        item_value = Decimal(0)
        age_value = 0
        date_of_birth = None
        # dependant
        dependant_name = None
        dependant_date_of_birth = None
        dependant_relationship = None

        if type_coverage_for == CountryBenefit.COVERAGE_FOR_EMPLOYEE:
            coverage_for_desc = "Employee"
        elif type_coverage_for == CountryBenefit.COVERAGE_FOR_DEPENDANT:
            coverage_for_desc = "Dependant"
            dependant = user_benefits_service.get_benefit_item_dependant(
                ubi.id
            )
            if dependant:
                dependant_name = dependant.name
                dependant_date_of_birth = dependant.date_of_birth
                dependant_relationship = dependant.relationship
                coverage_for_desc = (
                    f"{dependant_relationship} ({dependant_name})"
                )

        item_data = user_benefits_service.calc_benefit_item_value(
            benefit_item=ubi,
            year=ub.effective_from.year,
            month=ub.effective_from.month,
        )
        if item_data:
            range_value = item_data.range.value
            cell_value = item_data.cell.value
            range_id = item_data.range.id
            range_value = item_data.range.value
            cell_id = item_data.cell.id
            cell_value = item_data.cell.value
            monthly_salary = item_data.monthly_salary
            age_value = item_data.age
            date_of_birth = item_data.date_of_birth
            if item_data.gross_income:
                gross_income_id = item_data.gross_income.id
                gross_income_annual_salary = (
                    item_data.gross_income.annual_salary
                )

            if type_reference == CountryBenefit.RANGE_REFERENCE_AGE:
                range_reference_desc = (
                    f"Age - From {int(range_value)} years old"
                )
            elif type_reference == CountryBenefit.RANGE_REFERENCE_DEPENDANTS:
                range_reference_desc = (
                    f"Dependants - From {int(range_value)} dependants"
                )
            elif type_reference == CountryBenefit.RANGE_REFERENCE_GROSS_INCOME:
                range_reference_desc = (
                    f"Income - From {currency} {range_value:,.2f}"
                )

            if type_calc_type == CountryBenefit.CALC_TYPE_FIXED_AMOUNT:
                monthly_benefits = f"{currency} {cell_value:,.2f}"
            elif type_calc_type == CountryBenefit.CALC_TYPE_RATE:
                if (
                    type_rate_base
                    == CountryBenefit.RATE_BASE_GROSS_INCOME_MONTHLY
                ):
                    monthly_benefits = f"{cell_value:.2f}% of Gross Income"

            if item_data.value is not None:
                item_value = item_data.value

        return UserBenefitsItemDetailResponse(
            id=ubi.id,
            user_id=ubi.user_id,
            benefits_id=ubi.benefits_id,
            # benefit type
            benefit_type_id=ubi.benefit_type_id,
            benefit_type_name=benefit_type.name,
            benefit_type_country_id=benefit_type.country_id,
            benefit_type_mandatory=benefit_type.is_mandatory,
            benefit_type_link=benefit_type.link,
            benefit_type_link_desc=benefit_type.link_desc,
            benefit_type_coverage_for=benefit_type.coverage_for,
            benefit_type_calc_type=benefit_type.calc_type,
            benefit_type_rate_base=benefit_type.rate_base,
            benefit_type_range_reference=benefit_type.range_reference,
            # package & range & cell
            package_id=package.id if package else 0,
            package_name=package.name if package else "",
            range_id=range_id,
            range_value=range_value,
            cell_id=cell_id,
            cell_value=cell_value,
            # dependant
            dependant_name=dependant_name,
            dependant_date_of_birth=dependant_date_of_birth,
            dependant_relationship=dependant_relationship,
            # value
            gross_income_id=gross_income_id,
            gross_income_annual_salary=gross_income_annual_salary,
            monthly_salary=monthly_salary,
            num_of_dependants=ubi.num_of_dependants,
            age=age_value,
            date_of_birth=date_of_birth,
            coverage_for_desc=coverage_for_desc,
            range_reference_desc=range_reference_desc,
            monthly_benefits=monthly_benefits,
            value=item_value,
        )


class UserBenefitsDetailResponse(BaseModel):
    id: int
    user_id: int
    operator_id: Optional[int] = None
    effective_from: date
    effective_to: Optional[date] = None
    version: Optional[str] = None
    is_active: bool = False
    is_upcoming: bool = False
    items: List[UserBenefitsItemDetailResponse] = []

    @classmethod
    def from_model(cls, ub: UserBenefits):
        items: List[UserBenefitsItem] = list(
            ub.benefits_items.select_related(
                "benefit_type",
                "benefit_type__country",
            ).filter(has_delete=False)
        )
        packages = list(
            CountryBenefitPackage.objects.filter(
                benefit_id__in=[i.benefit_type_id for i in items],
            ).all()
        )
        items_data: List[UserBenefitsItemDetailResponse] = []
        for item in items:
            pkg = [
                package
                for package in packages
                if package.name == item.package
                and package.benefit_id == item.benefit_type_id
            ]
            items_data.append(
                UserBenefitsItemDetailResponse.from_model(
                    ub,
                    item,
                    package=pkg[0] if pkg else None,
                )
            )

        return UserBenefitsDetailResponse(
            id=ub.id,
            user_id=ub.user_id,
            operator_id=ub.operator_id,
            effective_from=ub.effective_from,
            effective_to=ub.effective_to,
            version=ub.version,
            items=items_data,
        )


class UserBenefitsDetailListResponse(BaseModel):
    list: List[UserBenefitsDetailResponse] = []


class UpdateUserBenefitsDependantsRequest(BaseModel):
    user_id: int
    operator_id: Optional[int] = None
    dependants: List[UserBenefitDependantRequest]
