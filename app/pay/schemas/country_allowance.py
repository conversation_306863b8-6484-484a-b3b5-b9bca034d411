from typing import List, Optional

from pydantic import BaseModel

from adm.models import CountryAllowance


class QueryCountryAllowanceRequest(BaseModel):
    country: int


class CountryAllowanceResponse(BaseModel):
    id: int
    country_id: int
    type_id: int
    type_name: str
    currency: str
    contractual: int
    min_amount: Optional[float] = None
    max_amount: Optional[float] = None

    @classmethod
    def from_model(cls, ca: CountryAllowance):
        return CountryAllowanceResponse(
            id=int(ca.id),
            country_id=ca.country_id,
            type_id=ca.allowance_type.id,
            type_name=ca.allowance_type.name,
            currency=ca.currency or ca.country.currency or "",
            contractual=ca.contractual,
            min_amount=ca.min_amount,
            max_amount=ca.max_amount,
        )
