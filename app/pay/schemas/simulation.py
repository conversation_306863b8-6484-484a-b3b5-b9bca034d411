from datetime import date

from pydantic import BaseModel, Field


class SimulationRequest(BaseModel):
    employee_id: int
    breadown_type: int = Field(
        default=3,
        description="Summary-1, Simple breakdown-2, Full breakdown-3",
    )
    onetime_fee_selected: bool = Field(default=False)
    deposit_selected: bool = Field(default=False)
    deposit_month: int = Field(default=0)


class SimulationResponse(BaseModel):
    file_url: str
    file_name: str


class EmployeeCostSimulationResponse(BaseModel):
    status: bool = Field(default=True)
    message: str = Field(default=None)
    items: list
    statutory_burden_items: list
    monthly_income: str
    converted_monthly_income: str
    monthly_income_items: list
    monthly_fee: str
    converted_monthly_fee: str
    employer_cost: str
    statutory_burden_cost: str
    converted_employer_cost: str
    converted_statutory_burden_cost: str
    burden_percentage: str
    total: str
    converted_total: str
    currency: str
    billing_currency: str
    country: str
    fx_rate: str

    class Config:
        schema_extra = {
            "examples": {
                "status": True,
                "message": None,
                "items": [
                    {
                        "name": "Disable Fee and Major Disease",
                        "cal_type": "Rate",
                        "category": "Employer Burden",
                        "sub_category": "Statutory",
                        "rate_field": {
                            "value": "1.6%",
                            "base": "Gross Income",
                            "cap_value": None,
                        },
                        "amount": "160.00",
                        "expressions": [["10000", "1.6%"]],
                        "converted_amount": "160.00",
                    },
                    {
                        "name": "Housing Funding",
                        "cal_type": "Rate",
                        "category": "Employer Burden",
                        "sub_category": "Statutory",
                        "rate_field": {
                            "value": "12%",
                            "base": "Gross Income",
                            "cap_value": "28221.00",
                        },
                        "amount": "1200.00",
                        "expressions": [["10000", "12%"]],
                        "converted_amount": "1200.00",
                    },
                    {
                        "name": "Medical and Maternity Insurance",
                        "cal_type": "Rate",
                        "category": "Employer Burden",
                        "sub_category": "Statutory",
                        "rate_field": {
                            "value": "9.8%",
                            "base": "Gross Income",
                            "cap_value": "28221.00",
                        },
                        "amount": "980.00",
                        "expressions": [["10000", "9.8%"]],
                        "converted_amount": "980.00",
                    },
                    {
                        "name": "Pension",
                        "cal_type": "Rate",
                        "category": "Employer Burden",
                        "sub_category": "Statutory",
                        "rate_field": {
                            "value": "16%",
                            "base": "Gross Income",
                            "cap_value": "28221.00",
                        },
                        "amount": "1600.00",
                        "expressions": [["10000", "16%"]],
                        "converted_amount": "1600.00",
                    },
                    {
                        "name": "Unemployment",
                        "cal_type": "Rate",
                        "category": "Employer Burden",
                        "sub_category": "Statutory",
                        "rate_field": {
                            "value": "0.5%",
                            "base": "Gross Income",
                            "cap_value": "28221.00",
                        },
                        "amount": "50.00",
                        "expressions": [["10000", "0.5%"]],
                        "converted_amount": "50.00",
                    },
                    {
                        "name": "Work Injury",
                        "cal_type": "Rate",
                        "category": "Employer Burden",
                        "sub_category": "Statutory",
                        "rate_field": {
                            "value": "0.6%",
                            "base": "Gross Income",
                            "cap_value": "28221.00",
                        },
                        "amount": "60.00",
                        "expressions": [["10000", "0.6%"]],
                        "converted_amount": "60.00",
                    },
                    {
                        "name": "Employer Liability Insurance",
                        "cal_type": "Rate",
                        "category": "Employer Burden",
                        "sub_category": "Others",
                        "rate_field": {
                            "value": "0.5%",
                            "base": "Gross Income",
                            "cap_value": None,
                        },
                        "amount": "50.00",
                        "expressions": [["10000", "0.5%"]],
                        "converted_amount": "50.00",
                    },
                    {
                        "name": "Local Other001",
                        "cal_type": "Rate",
                        "category": "Employer Burden",
                        "sub_category": "Others",
                        "rate_field": {
                            "value": "10%",
                            "base": "Gross Income",
                            "cap_value": None,
                        },
                        "amount": "1000.00",
                        "expressions": [["10000", "10%"]],
                        "converted_amount": "1000.00",
                    },
                ],
                "statutory_burden_items": [
                    {
                        "name": "Disable Fee and Major Disease",
                        "cal_type": "Rate",
                        "category": "Employer Burden",
                        "sub_category": "Statutory",
                        "rate_field": {
                            "value": "1.6%",
                            "base": "Gross Income",
                            "cap_value": None,
                        },
                        "amount": "160.00",
                        "expressions": [["10000", "1.6%"]],
                        "converted_amount": "160.00",
                    },
                    {
                        "name": "Housing Funding",
                        "cal_type": "Rate",
                        "category": "Employer Burden",
                        "sub_category": "Statutory",
                        "rate_field": {
                            "value": "12%",
                            "base": "Gross Income",
                            "cap_value": "28221.00",
                        },
                        "amount": "1200.00",
                        "expressions": [["10000", "12%"]],
                        "converted_amount": "1200.00",
                    },
                    {
                        "name": "Medical and Maternity Insurance",
                        "cal_type": "Rate",
                        "category": "Employer Burden",
                        "sub_category": "Statutory",
                        "rate_field": {
                            "value": "9.8%",
                            "base": "Gross Income",
                            "cap_value": "28221.00",
                        },
                        "amount": "980.00",
                        "expressions": [["10000", "9.8%"]],
                        "converted_amount": "980.00",
                    },
                    {
                        "name": "Pension",
                        "cal_type": "Rate",
                        "category": "Employer Burden",
                        "sub_category": "Statutory",
                        "rate_field": {
                            "value": "16%",
                            "base": "Gross Income",
                            "cap_value": "28221.00",
                        },
                        "amount": "1600.00",
                        "expressions": [["10000", "16%"]],
                        "converted_amount": "1600.00",
                    },
                    {
                        "name": "Unemployment",
                        "cal_type": "Rate",
                        "category": "Employer Burden",
                        "sub_category": "Statutory",
                        "rate_field": {
                            "value": "0.5%",
                            "base": "Gross Income",
                            "cap_value": "28221.00",
                        },
                        "amount": "50.00",
                        "expressions": [["10000", "0.5%"]],
                        "converted_amount": "50.00",
                    },
                    {
                        "name": "Work Injury",
                        "cal_type": "Rate",
                        "category": "Employer Burden",
                        "sub_category": "Statutory",
                        "rate_field": {
                            "value": "0.6%",
                            "base": "Gross Income",
                            "cap_value": "28221.00",
                        },
                        "amount": "60.00",
                        "expressions": [["10000", "0.6%"]],
                        "converted_amount": "60.00",
                    },
                ],
                "monthly_income": "20000.00",
                "converted_monthly_income": "20000.00",
                "monthly_income_items": [
                    {
                        "name": "Monthly Gross Income",
                        "amount": "10000.00",
                        "converted_amount": "10000.00",
                    },
                    {
                        "name": "Monthly Allowance",
                        "amount": "10000.00",
                        "converted_amount": "10000.00",
                    },
                ],
                "monthly_fee": "0.00",
                "converted_monthly_fee": "0.00",
                "employer_cost": "5100.00",
                "statutory_burden_cost": "4050.00",
                "converted_employer_cost": "5100.00",
                "converted_statutory_burden_cost": "4050.00",
                "burden_percentage": "25.50%",
                "total": "25100.00",
                "converted_total": "25100.00",
                "currency": "CNY",
                "billing_currency": "CNY",
                "country": "China",
                "fx_rate": "1",
            }
        }


class EmployeeCostSimulationRequest(BaseModel):
    country_id: int
    gross_income: float
    billing_currency: str
    work_permit_requirement: int = Field(default=0)
    region_id: int = Field(default=0)
