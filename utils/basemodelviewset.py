from django.core import exceptions
from django.http import QueryDict
from django.shortcuts import get_object_or_404
from rest_framework import permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet

from utils.constant import ROLE


class BaseViewSet(ModelViewSet):
    serializer_detail_created = False
    serializer_detail_class = None
    direct_report_exclude_condition = [
        ROLE.LEVEL_1.value,
        ROLE.TEAM_LEVEL_1.value,
    ]
    lookup_permission_field = "pk"
    extra_lookup_permission_field = None
    per_map = {}
    allowed_http_action = []

    _error_messages = {
        "notset": "The {field} is not set.",
        "mpass": "mandatory parameters: {field}",
    }

    def initialize_request(self, request, *args, **kwargs):
        request = super().initialize_request(request, *args, **kwargs)
        if (
            isinstance(request.query_params, QueryDict)
            and request.method in permissions.SAFE_METHODS
        ):
            request.query_params._mutable = True
        elif isinstance(request.data, QueryDict):
            request.data._mutable = True
        return request

    def get_object(self):
        obj = get_object_or_404(self.queryset, pk=self.kwargs["pk"])
        self.check_object_permissions(self.request, obj)
        return obj

    def get_serializer(self, *args, **kwargs):
        if "data" in kwargs:
            data = kwargs["data"]
            # check if many is required
            if isinstance(data, list):
                kwargs["many"] = True

        return super().get_serializer(*args, **kwargs)

    def fail(self, key, **kwargs):
        return self._error_messages[key].format(**kwargs)

    def create(self, request, *args, **kwargs):
        if self.serializer_detail_created:
            assert hasattr(
                self, "serializer_detail_class"
            ), "class must have serializer_detail_class attribute."
            self.serializer_class = self.serializer_detail_class
        return super().create(request, *args, **kwargs)

    @action(detail=False, methods=["GET"], url_path="check-name")
    def check_name(self, request, *args, **kwargs):
        unique_field = request.query_params.pop("unique_field", [0])[0]
        assert unique_field, "mandatory parameters: unique_field"
        try:
            if self.queryset.filter(
                **dict(request.query_params.items())
            ).exists():
                return Response(data=[f"{unique_field} already exists."])
        except exceptions.FieldError:
            assert False, "The model does not contain this field."
        return Response()
