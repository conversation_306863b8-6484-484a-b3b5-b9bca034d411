import pandas as pd
from django.db import transaction
from rest_framework import serializers

from app.pay.services import payment_rate_service
from pay.models import FXRate, MonthlySettings


class FXRateSerializers(serializers.Serializer):
    file = serializers.FileField(use_url=False, required=True)
    month = serializers.DateField(required=True)

    def save(self):
        data = pd.read_csv(self.validated_data["file"]).fillna("")
        month = self.validated_data["month"]
        peo_id = self.context["request"].user.provider_id
        created_obj, duplicated_currency = list(), list()
        origin_fx_markup = payment_rate_service.get_markup(
            month.year, month.month
        )

        for _, row in data.iterrows():
            try:
                ee_currency, billing_currency = (
                    row["Employee Currency"].strip(),
                    row["Billing Currency"].strip(),
                )
                if not (ee_currency and billing_currency):
                    raise serializers.ValidationError(
                        "Currency cannot be empty. Please check."
                    )

                if payment_rate_service.check_pegged_currency_match(
                    ee_currency,
                    billing_currency,
                ):
                    cal_fx_markup = 0
                else:
                    cal_fx_markup = origin_fx_markup

                key = f"{ee_currency}-{billing_currency}"
                if key in duplicated_currency:
                    raise serializers.ValidationError(
                        "There are duplicated currencies pair. Please check."
                    )
                duplicated_currency.append(key)
                rate_1 = row["Base Rate"]
                # rate_2 = row["Payday Exchange rate"],
                if isinstance(rate_1, str):
                    rate_1 = None
                    print(rate_1)
                # if isinstance(rate_2, str):
                #     rate_2 = None
                created_obj.append(
                    FXRate(
                        invoice_rate=rate_1
                        if rate_1 == 1
                        else rate_1 * (1 + float(cal_fx_markup)),
                        payday_rate=rate_1,
                        month=month,
                        employee_currency=ee_currency,
                        billing_currency=billing_currency,
                        provider_id=peo_id,
                        markup=cal_fx_markup,
                        base_rate=rate_1,
                    )
                )
            except KeyError:
                raise serializers.ValidationError(
                    "Data in the file is not complete. Please check."
                )
        with transaction.atomic():
            FXRate.objects.filter(
                month__year=month.year,
                month__month=month.month,
                provider_id=peo_id,
            ).delete()
            FXRate.objects.bulk_create(created_obj)
