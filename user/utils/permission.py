from user.models import Permission, RolePermission, User
from utils import ONLY
from utils.constant import PermissionContextItemSeparator

UNFINISHED_EXP = 1
UNFINISHED_LER = 2
DIRECT_REP = 3
USER_GRP = 4
APPROVER_PRO = 5
UNARCHIVED_VARIABLE_PAY = 6
END_DATE = 7

ARCHIVED_USER_MESSAGE = {
    UNFINISHED_EXP: "The user cannot be archived, because he has unfinished expenses.",
    UNFINISHED_LER: "The user cannot be archived, because he has unfinished leave requests.",
    DIRECT_REP: "The user cannot be archived, because he is someone's supervisor.",
    USER_GRP: "The user cannot be archived, because he is the only member in an approver group.",
    APPROVER_PRO: "The user cannot be archived, because he is set as approver in an approval process.",
    UNARCHIVED_VARIABLE_PAY: "This employee cannot be archived because of upcoming variable pay.",
    END_DATE: "Please fill in the end date of this employee before archiving.",
}

# check am
AM_CONTEXT = "PEOTier"


def check_user_archived(user: User) -> str:
    """
    If this user has unfinished expenses in process
    If this user has unfinished leave requests in process(include carryover request)
    If this user is set as someone else's supervisor
    If this user is the only one member of a approver group
    If this user is involved in an expense process or leave process directly
    If this user has unarchived variable pay
    If this user's end date is filled
    """
    code = 0
    if user.report.filter(
        has_reject=False, process_step__isnull=False, has_finish=False
    ).exists():
        code = UNFINISHED_EXP
    elif (
        user.apply_leaves.filter(has_finish=False).exists()
        or user.apply_carryovers.filter(has_finish=False).exists()
    ):
        code = UNFINISHED_LER
    elif User.objects.filter(reporter=user).exists():
        code = DIRECT_REP
    elif user.steps.exists() or user.step.exists():
        code = APPROVER_PRO
    elif user.variable_pay.filter(has_archived=False).exists():
        code = UNARCHIVED_VARIABLE_PAY
    elif (
        User.CORE_USER == user.user_type
        and User.STANDARD_COMPANY == user.work_for_type
        and not user.end_date
    ):
        code = END_DATE
    else:
        for admin_group in user.admin_group.select_related(
            "admin_group"
        ).all():
            if admin_group.admin_group.members.count() == ONLY:
                code = USER_GRP
                break
    return ARCHIVED_USER_MESSAGE.get(code, "")


def get_permission_context_list(context: str) -> [str]:
    return context.split(PermissionContextItemSeparator) if context else []


def get_permission_context_string(cl: str) -> str:
    return cl


def get_role_permission_list(role_id: int, return_codes=False):
    ids = RolePermission.objects.filter(role_id=role_id).values_list(
        "permission_id", flat=True
    )
    queryset = Permission.objects.filter(id__in=ids)
    if return_codes:
        return queryset.values_list("code", flat=True)
    return queryset


def get_current_permission_context_list():
    ...


def check_am_permission(user):
    if hasattr(user, "_context"):
        return AM_CONTEXT == user._context
    return False
