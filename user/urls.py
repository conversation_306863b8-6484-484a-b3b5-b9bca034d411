from django.urls import path
from rest_framework.routers import Default<PERSON><PERSON><PERSON>

from user.views.auth import check_email
from user.views.common import (
    SettingView,
    check_iban,
    currency_rate,
    query_account_manager,
    query_partner_owner,
    sync_payslips,
)
from user.views.register import RegisterUserView
from user.views.user import UserView

urlpatterns = [
    path("common/currency-rate/", currency_rate),
    path("check-email/", check_email),
    path("check-iban/", check_iban),
    path("sync-payslips/", sync_payslips),
    path("account-manager/", query_account_manager),
    path("partner-owner/", query_partner_owner),
]
router = DefaultRouter()

router.register("setting", SettingView)
router.register("register", RegisterUserView)
router.register("user", UserView)

urlpatterns += router.urls
