import calendar
import datetime
import operator
from functools import reduce
from typing import List

from django.db.models import Q
from django.utils import timezone
from django_filters import filters

from adm.models import LocalEntity, TeamEntityProviderRef
from pay.models import (
    STATUS_LOCKED,
    STATUS_NOT,
    InvoiceSummaryItem,
    PayrollReport,
    ServiceFee,
)
from user.models import (
    Permission,
    Role,
    RolePermission,
    User,
    UserDocument,
    UserPlatformSetting,
)
from user.nodes import WORK_FOR_PEO, WORK_FOR_TEAM
from user.utils.common import get_filter_condition
from utils.basefield import IntegerFilter
from utils.basefilter import BaseGraphQLFilter
from utils.constant import STANDARD_COMPANY_TYPE
from utils.constant import Permission as PermissionEnum
from utils.constant import PermissionContext as PermissionContextEnum
from utils.cryptography import filter_encrypt_users

DEFAULT_ROLE_FILTER = dict(
    provider_id__isnull=True,
    local_entity_id__isnull=True,
    team_id__isnull=True,
)

WORK_FOR_TYPE_CONVERT = {
    WORK_FOR_PEO: User.PEO,
    WORK_FOR_TEAM: User.STANDARD_COMPANY,
}


def do_upcoming_status_filter(queryset, _, value):
    return (
        queryset.filter(phase=User.UPCOMING_PHASE)
        .select_related("onboarding_user")
        .filter(onboarding_user__info_status=value)
    )


def do_name_filter(queryset, _, value):
    return filter_encrypt_users(value, queryset, queryset)


def do_filter(queryset, name, value):
    value = value.split(",")

    if name != "work_for":
        condition = {
            "local_entity": Q(company_id__in=value),
            "role": Q(role_id__in=value),
            "expense_process": Q(expense_process_id__in=value),
            "leave_process": Q(usersetting__leave_process_id__in=value),
        }[name]
    else:
        condition = reduce(
            operator.or_,
            (
                Q(work_for_type=WORK_FOR_TYPE_CONVERT[v[0]], work_for=v[1:])
                for v in value
            ),
        )

    return queryset.filter(condition)


# def do_biller_filter(q)


def do_team_document_filter(queryset, _, value):
    arr = value.split("|")
    work_for_type = arr[0]
    work_for_id = arr[1]
    provider_id = arr[2]

    local_entities = LocalEntity.objects.filter(
        provider_id=provider_id
    ).values_list("id", flat=True)

    return queryset.filter(
        Q(
            work_for_type=work_for_type,
            work_for=work_for_id,
            employer_of_record=provider_id,
        )
        | Q(
            work_for_type=work_for_type,
            work_for=work_for_id,
            company_id__in=local_entities,
        )
    ).distinct()


class RoleFilter(BaseGraphQLFilter):
    name = filters.CharFilter(lookup_expr="istartswith")
    default_role = filters.BooleanFilter(method="do_default_role_filter")
    default_role_and_provider_id = filters.NumberFilter(
        method="do_default_role_and_provider_id_filter"
    )
    provider_id = filters.NumberFilter(method="do_provider_id_filter")
    scope = filters.CharFilter(method="do_scope_filter")
    team = filters.NumberFilter(
        method="do_peo_and_team_filter",
        label="Query default roleand role associated with the team",
    )

    def _filter_by_current_user_permission(
        self, queryset, exclude_context_list: List[PermissionContextEnum]
    ):
        user_role = self.request.user.role_id

        if RolePermission.objects.filter(
            role_id=user_role,
            code=PermissionEnum.ROLE_FULLACCESS,
            context=PermissionContextEnum.AllTier.value,
        ).exists():
            return queryset

        role_permissions = RolePermission.objects.filter(
            role_id=user_role
        ).values_list("permission_id", flat=True)
        exclude_permissions = Permission.objects.exclude(
            pk__in=role_permissions
        ).values_list("id", flat=True)
        exclude_role = RolePermission.objects.filter(
            Q(permission_id__in=exclude_permissions)
            | Q(
                permission_id__in=role_permissions,
                context__in=exclude_context_list,
            )
        ).values_list("role_id", flat=True)

        return queryset.exclude(pk__in=set(exclude_role))

    @staticmethod
    def do_default_role_filter(queryset, _, value):
        if not value:
            return queryset
        return queryset.filter(**DEFAULT_ROLE_FILTER)

    def do_default_role_and_provider_id_filter(self, queryset, _, value):
        all_and_provider_role_ids = self._filter_by_current_user_permission(
            queryset.filter(
                Q(
                    scope__in=[Role.SCOPE_ALL, Role.SCOPE_PROVIDER],
                    **DEFAULT_ROLE_FILTER,
                )
                | Q(provider_id=value)
            ),
            [PermissionContextEnum.AllTier.value],
        ).values_list("id", flat=True)

        team_role_ids = queryset.filter(
            Q(scope=Role.SCOPE_TEAM, **DEFAULT_ROLE_FILTER)
            | Q(provider_id=value)
        ).values_list("id", flat=True)

        return (
            queryset.filter(
                pk__in=list(all_and_provider_role_ids) + list(team_role_ids)
            )
            .distinct()
            .order_by("provider_id", "id")
        )

    @staticmethod
    def do_provider_id_filter(queryset, _, value):
        if value == -1:
            return queryset.filter(provider_id__isnull=False)
        return queryset.filter(provider_id=value)

    @staticmethod
    def do_scope_filter(queryset, _, value):
        return queryset.filter(scope__in=value.split(","))

    def do_peo_and_team_filter(self, queryset, _, value):
        scopes = [Role.SCOPE_ALL, Role.SCOPE_TEAM]
        trs = TeamEntityProviderRef.objects.filter(team_id=value).values_list(
            "employer_of_record", "employer_of_record_type", named=True
        )
        peos = filter(
            lambda x: TeamEntityProviderRef.PEO == x.employer_of_record_type,
            trs,
        )
        teams = filter(
            lambda x: TeamEntityProviderRef.STANDARD_COMPANY
            == x.employer_of_record_type,
            trs,
        )

        queryset = queryset.filter(
            Q(scope__in=scopes, team_id__isnull=True, provider_id__isnull=True)
            | Q(team_id=value)
            | Q(provider_id__in=(peo.employer_of_record for peo in peos))
            | Q(team_id__in=(team.employer_of_record for team in teams))
        )
        return self._filter_by_current_user_permission(
            queryset, [PermissionContextEnum.AllTier.value]
        ).distinct()

    class Meta:
        model = Role
        fields = [
            "name",
            "default_role",
            "provider_id",
            "default_role_and_provider_id",
            "scope",
            "team",
            "role_type",
        ]


class UserFilter(BaseGraphQLFilter):
    name = filters.CharFilter(method=do_name_filter)
    work_for = filters.CharFilter(method=do_filter)
    local_entity = filters.CharFilter(method=do_filter)
    role = filters.CharFilter(method=do_filter)
    leave_process = filters.CharFilter(method=do_filter)
    expense_process = filters.CharFilter(method=do_filter)
    exclude_user = IntegerFilter(label="Exlcude user ID")
    user_type = filters.CharFilter(method="do_user_type_filter")
    account_manager = IntegerFilter(method="do_account_manager_filter")
    billing_group_id = IntegerFilter(method="do_billing_group_id_filter")

    filter_team_document = filters.CharFilter(
        method=do_team_document_filter,
        label="""
        This parameter is used for document user list
        eg. teamId|providerId => 1|2
    """,
    )

    email = filters.CharFilter(method="do_email_filter")
    upcoming_status = IntegerFilter(method=do_upcoming_status_filter)
    phases = filters.CharFilter(method="do_phases_filter")
    phase = IntegerFilter()

    UPCOMING_PHASE = User.UPCOMING_PHASE

    def _parse_date_and_status(self, value):
        """
        "2022-06,1" => 2022, 6, 1
        """
        params = value.split(",")
        date, status = params[0].split("-"), params[1]
        year, month = int(date[0]), int(date[1])
        return year, month, int(status)

    @staticmethod
    def do_email_filter(queryset, _, value):
        return queryset.filter_by_encrypted_field(
            value,
            field_name="email",
        )

    def do_user_type_filter(self, queryset, _, value):
        if value == f"{User.COLLABORATOR}":
            setattr(self.request.user, "_context", "All")
        return queryset.filter(user_type=value)

    def do_account_manager_filter(self, queryset, _, value):
        user_settings = UserPlatformSetting.objects.filter(
            am_id=value
        ).values_list("user_id", flat=True)
        return queryset.filter(pk__in=user_settings)

    def do_billing_group_id_filter(self, queryset, _, value):
        return queryset.filter(billing_group_id=value)

    @staticmethod
    def do_phases_filter(queryset, _, value):
        return queryset.filter(phase__in=value.split(","))

    class Meta:
        model = User
        fields = [
            "name",
            "location_city",
            "location_country",
            "work_for",
            "local_entity",
            "filter_team_document",
            "user_type",
            "role",
            "expense_process",
            "leave_process",
            "email",
            "team",
            "phase",
            "phases",
            "id",
            "account_manager",
        ]

    @property
    def qs(self):
        if self.data["phase"] is None and self.data["phases"] is None:
            self.data["phase"] = User.ACTIVE_PHASE
        if self.data.get("exclude_user"):
            exclude_user = self.data.pop("exclude_user")
            return (
                super()
                .qs.filter(
                    get_filter_condition(
                        self.request.user.provider_id, self.data["user_type"]
                    )
                )
                .exclude(pk=exclude_user)
            )
        return super().qs.filter(
            get_filter_condition(
                self.request.user.provider_id, self.data["user_type"]
            )
        )


class InternalUserFilter(BaseGraphQLFilter):
    name = filters.CharFilter(method=do_name_filter)
    work_for = filters.CharFilter(method=do_filter)
    local_entity = filters.CharFilter(method=do_filter)
    role = filters.CharFilter(method=do_filter)
    # biller = filters.CharFilter(method=do_biller_filter)
    leave_process = filters.CharFilter(method=do_filter)
    expense_process = filters.CharFilter(method=do_filter)
    exclude_user = IntegerFilter(label="Exclude user ID")

    filter_team_document = filters.CharFilter(
        method=do_team_document_filter,
        label="""
        This parameter is used for document user list
        eg. teamId|providerId => 1|2
    """,
    )

    class Meta:
        model = User
        fields = [
            "name",
            "location_city",
            "local_entity",
            "user_type",
            "role",
            "filter_team_document",
            "expense_process",
            "leave_process",
            "phase",
        ]

    @property
    def qs(self):
        if self.data.get("phase") is None:
            self.data["phase"] = User.ACTIVE_PHASE
        if self.data.get("exclude_user"):
            exclude_user = self.data.pop("exclude_user")
            return (
                super()
                .qs.filter(provider_id=self.request.user.provider_id)
                .exclude(pk=exclude_user)
            )
        return super().qs.filter(provider_id=self.request.user.provider_id)


class UserDocumentFilter(BaseGraphQLFilter):
    team = filters.NumberFilter(method="do_team_filter")
    my = filters.BooleanFilter(method="do_my_filter")
    user_id = filters.NumberFilter(method="do_user_id_filter")
    kinds = filters.CharFilter(method="do_kinds_filter")
    team_uploader = filters.NumberFilter(method="do_team_uploader_filter")
    date = filters.DateFilter(method="do_date_filter")

    def do_team_filter(self, queryset, _, value):
        users = User.objects.filter(
            work_for_type=User.STANDARD_COMPANY,
            work_for=value,
        ).values_list("id", flat=True)
        return queryset.filter(user_id__in=users)

    def do_my_filter(self, queryset, _, value):
        if not value:
            return queryset.none()
        return queryset.filter(user_id=self.request.user.id)

    def do_user_id_filter(self, queryset, _, value):
        return queryset.filter(user_id=value)

    def do_team_uploader_filter(self, queryset, _, value):
        users = User.objects.filter(
            work_for_type=User.STANDARD_COMPANY,
            work_for=value,
        ).values_list("id", flat=True)
        return queryset.filter(uploader_id__in=users)

    def do_phases_filter(queryset, _, value):
        if not value:
            return queryset.none()
        return queryset.filter(kind__in=value.split(","))

    def do_kinds_filter(self, queryset, _, value):
        return queryset.filter(kind__in=value.split(","))

    def do_date_filter(self, queryset, _, value):
        return queryset.filter(date__year=value.year, date__month=value.month)

    class Meta:
        model = UserDocument
        fields = ["kind"]


class ActiveEmployeeFilter(BaseGraphQLFilter):
    name = filters.CharFilter(method=do_name_filter)
    start_date = filters.CharFilter(method="do_start_date_filter")  # 2022,3
    upcoming_status = IntegerFilter(method=do_upcoming_status_filter)
    phases = filters.CharFilter()
    location_country = IntegerFilter()
    phase = IntegerFilter()
    team = IntegerFilter(method="do_team_filter")
    # For Pay
    year = IntegerFilter()
    month = IntegerFilter()
    has_invoice = filters.BooleanFilter()

    # For payroll report, filtering using payrollreport.billing_group_id, not user.billing_group_id
    billing_group_id = IntegerFilter(method="do_billing_group_filter")

    class Meta:
        model = User
        fields = [
            "name",
            "location_city",
            "phase",
            "upcoming_status",
            "phases",
            "location_country",
            "team",
            "has_invoice",
        ]

    @staticmethod
    def do_start_date_filter(queryset, _, value):
        v = list(map(int, value.split(",")))
        _, days = calendar.monthrange(v[0], v[1])
        day = datetime.date(v[0], v[1], days)
        return queryset.filter(start_date__lte=day)

    @staticmethod
    def do_team_filter(queryset, _, value):
        return queryset.filter(team_id=value)

    @staticmethod
    def do_billing_group_filter(queryset, _, value):
        return (
            queryset.prefetch_related("payroll_reports")
            .filter(payroll_reports__billing_group_id=value)
            .distinct()
        )

    @staticmethod
    def do_phases_filter(queryset, value, year, month):
        return queryset.filter(phase__in=value.split(",")).exclude(
            Q(archived_at__year__lt=year)
            | Q(archived_at__year=year, archived_at__month__lt=month)
        )

    @property
    def qs(self):
        if self.data.get("phase") is None and self.data["phases"] is None:
            self.data["phase"] = User.ACTIVE_PHASE
        user = getattr(self.request, "user")
        if STANDARD_COMPANY_TYPE == user.work_for_type:
            self.data["team"] = team_id = user.team_id
        else:
            team_id = self.data["team"]
        phases, year, month, has_invoice, billing_group_id = map(
            self.data.pop,
            ["phases", "year", "month", "has_invoice", "billing_group_id"],
        )
        queryset = super().qs
        if phases:
            queryset = self.do_phases_filter(
                queryset,
                phases,
                year or timezone.now().year,
                month or timezone.now().month,
            )
        if billing_group_id:
            queryset = queryset.prefetch_related("payroll_reports").filter(
                payroll_reports__billing_group_id=billing_group_id,
                payroll_reports__year=year,
                payroll_reports__month=month,
                payroll_reports__team=team_id,
            )
        return queryset


class PartnerUserFilter(BaseGraphQLFilter):
    class Meta:
        model = User
        fields = ["work_for"]


class PartnerEmployeeFilter(BaseGraphQLFilter):
    name = filters.CharFilter(method=do_name_filter)
    phases = filters.CharFilter(
        method="do_phases_filter", empty_value=f"{User.ACTIVE_PHASE}"
    )
    start_date = filters.CharFilter(method="do_start_date_filter")  # 2022,3
    end_date = filters.CharFilter(method="do_end_date_filter")  # 2022,3

    class Meta:
        model = User
        fields = ["name"]

    @staticmethod
    def do_phases_filter(queryset, _, value):
        return queryset.filter(phase__in=value.split(","))

    @staticmethod
    def do_start_date_filter(queryset, _, value):
        v = list(map(int, value.split(",")))
        _, days = calendar.monthrange(v[0], v[1])
        day = datetime.date(v[0], v[1], days)
        return queryset.filter(start_date__lte=day)

    @staticmethod
    def do_end_date_filter(queryset, _, value):
        v = list(map(int, value.split(",")))
        day = datetime.date(v[0], v[1], 1)
        return queryset.filter(Q(end_date__isnull=True) | Q(end_date__gte=day))

    @property
    def qs(self):
        current_user = self.request.user
        partner_id = current_user.work_for
        queryset = super().qs

        if current_user.work_for_type != User.PARTNER_TYPE:
            return queryset.none()

        user_settings = UserPlatformSetting.objects.filter(
            payroll_partner_id=partner_id
        )
        queryset = queryset.filter(
            pk__in=user_settings.values_list("user_id", flat=True)
        )
        return queryset


class ActiveOtherFilter(BaseGraphQLFilter):
    name = filters.CharFilter(method=do_name_filter)
    phase = IntegerFilter()

    class Meta:
        model = User
        fields = ["name", "email", "phase"]

    @property
    def qs(self):
        if self.data["phase"] is None:
            self.data["phase"] = User.ACTIVE_PHASE
        return super().qs.filter(team_id=self.request.user.team_id)


class EmployeeDocumentFilter(BaseGraphQLFilter):
    name = filters.CharFilter(method=do_name_filter)

    class Meta:
        model = User
        fields = [
            "name",
        ]

    @property
    def qs(self):
        user = self.request.user
        parent = super().qs
        if STANDARD_COMPANY_TYPE == user.work_for_type:
            return parent.filter(
                team_id=user.work_for, user_type=User.CORE_USER
            )
        return parent.none()
