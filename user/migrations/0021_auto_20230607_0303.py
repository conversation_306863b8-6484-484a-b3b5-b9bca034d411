# Generated by Django 3.1 on 2023-06-07 03:03

from django.db import migrations


def init_join_contractor_role_and_permissions(apps, _):
    from user.models import Permission, Role, RolePermission

    r = Role(
        name="Contractor",
        description="",
        kind=Role.KIND_PRE_DEFINED,
        scope=Role.SCOPE_JOIN,
    )
    r.save()

    ps = [
        "Home.Contractor.FullAccess",
        "Join.Contractor.SelfProfile.FullAccess",
        "Join.Contractor.Invoice.FullAccess",
    ]
    for perm in ps:
        p = Permission(
            code=perm,
            scope=Permission.SCOPE_JOIN,
            category="Join",
        )
        p.save()

        rp = RolePermission(
            role_id=r.id,
            code=p.code,
            permission_id=p.id,
        )
        rp.save()

    p1 = Permission.objects.filter(code="Join.Admin.MyAccount.FullAccess").first()
    p2 = Permission.objects.filter(code="Join.Contractor.Myonboarding.FullAccess").first()
    p3 = Permission.objects.filter(code="User.UserProfile.Edit").first()
    if p1:
        rp = RolePermission(
            role_id=r.id,
            code=p1.code,
            permission_id=p1.id,
        )
        rp.save()
    if p2:
        rp = RolePermission(
            role_id=r.id,
            code=p2.code,
            permission_id=p2.id,
        )
        rp.save()
    if p3:
        rp = RolePermission(
            role_id=r.id,
            code=p3.code,
            permission_id=p3.id,
        )
        rp.save()


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0020_auto_20230525_0641'),
    ]

    operations = [
        migrations.RunPython(
            init_join_contractor_role_and_permissions,
            reverse_code=migrations.RunPython.noop,
        )
    ]
