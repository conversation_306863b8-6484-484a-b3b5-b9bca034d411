# Generated by Django 3.1.13 on 2023-12-26 01:40

from django.db import migrations, models
import utils.cryptography
import utils.djangoplus.aws_s3_storage
import utils.utils


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0023_auto_20230926_0927'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='decoded_email',
            field=models.CharField(default='', max_length=200),
        ),
        migrations.AddField(
            model_name='user',
            name='decoded_firstname',
            field=models.CharField(default='', max_length=100),
        ),
        migrations.AddField(
            model_name='user',
            name='decoded_lastname',
            field=models.Char<PERSON>ield(default='', max_length=100),
        ),
    ]
