# Generated by Django 3.1 on 2021-06-02 09:07

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("adm", "0003_auto_20210526_0729"),
        ("user", "0002_auto_20210516_1113"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="nationality",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="user_nationality",
                to="adm.area",
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="user_type",
            field=models.SmallIntegerField(
                choices=[(1, "Core User"), (2, "Collaborator")], default=1
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="location_city",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="user_city",
                to="adm.area",
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="location_country",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="user_country",
                to="adm.area",
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="id_num_type",
            field=models.PositiveSmallIntegerField(
                choices=[(1, "National ID"), (2, "Passport ID")], default=1
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="work_for",
            field=models.PositiveIntegerField(
                null=True, verbose_name="PEO or Team ID"
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="work_for_type",
            field=models.PositiveSmallIntegerField(
                choices=[(1, "PEO Company"), (2, "Standard Company")],
                default=1,
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="employer_of_record",
            field=models.PositiveIntegerField(
                null=True, verbose_name="PEO or Team ID"
            ),
        ),
    ]
