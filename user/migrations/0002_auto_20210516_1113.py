# Generated by Django 3.1 on 2021-05-16 11:13

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("adm", "0001_initial"),
        ("user", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Permission",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("code", models.CharField(max_length=50, unique=True)),
                ("name", models.CharField(default="", max_length=50)),
                ("description", models.CharField(default="", max_length=200)),
                ("category", models.CharField(max_length=50)),
                (
                    "scope",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "Admin"), (2, "Explore"), (3, "People")],
                        default=1,
                    ),
                ),
                (
                    "context",
                    models.Char<PERSON>ield(blank=True, max_length=200, null=True),
                ),
            ],
            options={
                "db_table": "user_permission",
            },
        ),
        migrations.AddField(
            model_name="role",
            name="creator",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="creator",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="role",
            name="description",
            field=models.CharField(default="", max_length=200),
        ),
        migrations.AddField(
            model_name="role",
            name="kind",
            field=models.PositiveSmallIntegerField(
                choices=[(1, "PreDefined"), (2, "Custom")], default=1
            ),
        ),
        migrations.AddField(
            model_name="role",
            name="local_entity",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="roles",
                to="adm.localentity",
            ),
        ),
        migrations.AddField(
            model_name="role",
            name="provider",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="roles",
                to="adm.providerprofile",
            ),
        ),
        migrations.AddField(
            model_name="role",
            name="scope",
            field=models.PositiveSmallIntegerField(
                choices=[
                    (1, "None"),
                    (2, "All"),
                    (3, "Provider"),
                    (4, "LocalEntity"),
                    (5, "Team"),
                ],
                default=2,
            ),
        ),
        migrations.AddField(
            model_name="role",
            name="team",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="roles",
                to="adm.team",
            ),
        ),
        migrations.AlterField(
            model_name="role",
            name="name",
            field=models.CharField(default="", max_length=50),
        ),
        migrations.AlterUniqueTogether(
            name="role",
            unique_together={("name", "scope")},
        ),
        migrations.CreateModel(
            name="RolePermission",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("code", models.CharField(max_length=50)),
                (
                    "context",
                    models.CharField(blank=True, max_length=200, null=True),
                ),
                (
                    "permission",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="user.permission",
                    ),
                ),
                (
                    "role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="user.role",
                    ),
                ),
            ],
            options={
                "db_table": "user_role_permissions",
            },
        ),
    ]
