# Generated by Django 3.1 on 2022-06-09 14:05

from django.conf import settings
from django.db import migrations, models

from utils.djangoplus import bulk_update


def migrate_user_end_date(apps, _):
    if settings.CURRENT_ENV in [
        "LOCAL",
        "UNITTEST",
        "DEMO",
        "INTEGRATION",
        "STAGING",
    ]:
        return
    from onboarding.utils import get_onboarding_end_date
    from user.models import User

    objs = User.objects_all.filter(onboarding_user__isnull=False)
    for obj in objs:
        obj.end_date = get_onboarding_end_date(obj)

    bulk_update(objs, update_fields=["end_date"])


class Migration(migrations.Migration):

    dependencies = [
        ("user", "0014_auto_20220318_0418"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="end_date",
            field=models.DateField(db_index=True, null=True),
        ),
        migrations.RunPython(
            migrate_user_end_date, reverse_code=migrations.RunPython.noop
        ),
    ]
