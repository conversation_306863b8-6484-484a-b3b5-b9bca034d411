import graphene
from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction
from django.utils import timezone
from graphene import InputObjectType, ObjectType
from graphene.types.generic import GenericScalar
from jsonpath_rw import parse

from adm.models import AdminGroupMembers
from expenses.models import Expense<PERSON>tep
from leave import BALANCE_TYPE_P
from leave.models import LeaveProcessStep, LeaveTypePolicyRef
from onboarding.models import OnboardingUserBasicInfo
from onboarding.nodes.template import OnboardingTemplateSectionNode
from onboarding.utils import (
    BASIC_FIELDS_MAP,
    UPDATED_FIELDS,
    get_updated_field,
)
from user.models import User, UserSetting
from user.utils.common import USER_INFO_FIELD_MAP
from utils.constant import SOURCE
from utils.constant import Permission as PERMISSION
from utils.exceptionhandler import ErrMsg
from utils.permission import permissions_checker
from utils.utils import invert_dict


class UserSelfProfileNode(ObjectType):
    value = GenericScalar()
    display = graphene.List(OnboardingTemplateSectionNode)
    # Add validate
    country = graphene.Int()
    country_name = graphene.String()
    work_permit = graphene.Int()


class UserProfileObjectType(InputObjectType):
    user_id = graphene.Int(required=True)
    section = graphene.String(required=True)
    section_id = graphene.Int()
    source = graphene.Int(required=True)
    value = GenericScalar()
    field_name = graphene.String(required=True)


class UpdateUserProfileInput(InputObjectType):
    fields = graphene.List(UserProfileObjectType, required=True)
    category = graphene.String(required=True)
    has_validated = graphene.Boolean(default_value=False)


class UpdateUserProfileMutation(graphene.Mutation):
    class Arguments:
        data = UpdateUserProfileInput(required=True)

    status = graphene.Boolean()
    message = graphene.String()
    WARNNING_MSG = "The user's leave policy cannot be changed because of unfinished leave request."
    name_fields = ["firstname", "lastname", "other_name"]
    # frozen ordering
    validate_fields = ["Email", "Work for"]
    partner_field = "Payroll Partner"
    property_field = "Payroll Property"
    ID_TITLE = "ID"
    UPDATE_PROPERTY_ERROR = "Please remove current month’s PR from draft invoice before you update the payroll property"

    @classmethod
    @permissions_checker(permissions=[PERMISSION.USER_EDIT])
    def mutate(cls, _, info, data):
        login_user = info.context.user
        editable_field_names = list(
            login_user.get_editable_field_names_by_category(data.category)
        )
        inverted_basic_fields, inverted_user_fields = map(
            invert_dict, [BASIC_FIELDS_MAP, USER_INFO_FIELD_MAP]
        )
        # section_name = set()
        onboarding_user, user = None, None

        try:
            if data.has_validated:
                for field in data.fields:
                    field_name = field["field_name"]
                    if (
                        field_name not in cls.validate_fields
                        or not field["value"]
                    ):
                        continue
                    if field_name == cls.validate_fields[0]:
                        try:
                            onboarding_user = (
                                OnboardingUserBasicInfo.objects.get(
                                    id=field["user_id"]
                                )
                            )
                        except OnboardingUserBasicInfo.DoesNotExist:
                            return cls(status=False, message=ErrMsg.NODATAMSG)

                        if onboarding_user.email != field[
                            "value"
                        ] and OnboardingUserBasicInfo.check_unique(
                            field["value"]
                        ):
                            return cls(
                                status=False,
                                message=ErrMsg.FIELDEXISTMSG_ % "Email",
                            )
                    else:
                        if SOURCE.ONBOARDING_USER_INFO == field["source"]:
                            try:
                                onboarding_user = (
                                    OnboardingUserBasicInfo.objects.get(
                                        id=field["user_id"]
                                    )
                                )
                                user = onboarding_user.user_self
                            except OnboardingUserBasicInfo.DoesNotExist:
                                return cls(
                                    status=False, message=ErrMsg.NODATAMSG
                                )
                        else:
                            try:
                                user = User.objects.get(id=field["user_id"])
                            except User.DoesNotExist:
                                return cls(
                                    status=False, message=ErrMsg.NODATAMSG
                                )
                        if (user and user.team_id != int(field["value"])) and (
                            User.objects.filter(reporter=user).exists()
                            or LeaveProcessStep.objects.filter(
                                user=user
                            ).exists()
                            or ExpenseStep.objects.filter(user=user).exists()
                        ):
                            return cls(
                                status=False,
                                message="This user's work for cannot be changed.",
                            )

            for field in data.fields:
                field_name = field["field_name"]
                # section_name.add(field['section'])
                if field_name not in cls.name_fields:
                    if field_name not in editable_field_names:
                        return cls(status=False, message=ErrMsg.ROLEERRMSG)
                else:
                    if "Name" not in editable_field_names:
                        return cls(status=False, message=ErrMsg.ROLEERRMSG)
                source = field["source"]
                if source in [
                    SOURCE.TEMPLATE_USER_INFO,
                    SOURCE.CLIENT_TEMPLATE_USER_INFO,
                ]:
                    if onboarding_user is None:
                        try:
                            onboarding_user = (
                                OnboardingUserBasicInfo.objects.get(
                                    id=field["user_id"]
                                )
                            )
                        except OnboardingUserBasicInfo.DoesNotExist:
                            return cls(status=False, message=ErrMsg.NODATAMSG)
                    try:
                        orgin = {
                            SOURCE.TEMPLATE_USER_INFO: onboarding_user.user_infos,
                            SOURCE.CLIENT_TEMPLATE_USER_INFO: onboarding_user.client_hire.user_client_infos
                            if onboarding_user.client_hire
                            else None,
                        }[source].get(section=field["section_id"])
                    except ObjectDoesNotExist:
                        continue
                    content = orgin.content
                    content[field_name] = field["value"]
                    user_self = onboarding_user.user_self
                    if user_self:
                        if field_name in UPDATED_FIELDS:
                            res = dict()
                            get_updated_field(field_name, field["value"], res)
                            for k, v in res.items():
                                setattr(user_self, k, v)
                            user_self.save()
                        elif cls.ID_TITLE == field_name:
                            parser = parse("$.items.*")
                            try:
                                for id_infos in (
                                    m.value
                                    for m in parser.find(field["value"])
                                ):
                                    tp = id_infos["type"]
                                    if "select" == tp:
                                        user_self.id_num_type = (
                                            User.ID_NUM_TYPE_CONVERT[
                                                id_infos["value"]
                                            ]
                                        )
                                    elif "input" == tp:
                                        user_self.id_num = id_infos["value"]
                                user_self.save()
                            except KeyError:
                                ...
                    orgin.content = content
                    orgin.save()

                elif SOURCE.ONBOARDING_USER_INFO == source:
                    try:
                        onboarding_user = (
                            onboarding_user
                            or OnboardingUserBasicInfo.objects.get(
                                id=field["user_id"]
                            )
                        )
                    except OnboardingUserBasicInfo.DoesNotExist:
                        return cls(status=False, message=ErrMsg.NODATAMSG)

                    if field_name not in cls.name_fields:
                        try:
                            setattr(
                                onboarding_user,
                                inverted_basic_fields[field_name],
                                field["value"],
                            )
                        except ValueError:
                            setattr(
                                onboarding_user,
                                f"{inverted_basic_fields[field_name]}_id",
                                field["value"] or None,
                            )
                    else:
                        setattr(onboarding_user, field_name, field["value"])
                elif SOURCE.USER_INFO == source:
                    if user is None:
                        try:
                            user = User.objects.get(id=field["user_id"])
                        except User.DoesNotExist:
                            return cls(status=False, message=ErrMsg.NODATAMSG)
                    if field_name not in [
                        "Leave Process",
                        "Leave Policy",
                        "Approvers Group",
                    ]:
                        try:
                            setattr(
                                user,
                                inverted_user_fields[field_name],
                                field["value"],
                            )
                        except ValueError:
                            setattr(
                                user,
                                f"{inverted_user_fields[field_name]}_id",
                                field["value"] or None,
                            )
                        # user.save()
                    elif field_name == "Approvers Group":
                        with transaction.atomic():
                            AdminGroupMembers.objects.filter(
                                member=user
                            ).delete()
                            AdminGroupMembers.objects.bulk_create(
                                [
                                    AdminGroupMembers(
                                        admin_group_id=admin_group_id,
                                        member=user,
                                    )
                                    for admin_group_id in field["value"] or []
                                ]
                            )
                    else:
                        # Foreign related field
                        fd_name = f"{inverted_user_fields[field_name]}_id"
                        is_policy = "Leave Policy" == field_name
                        try:
                            usersetting = user.usersetting
                        except ObjectDoesNotExist:
                            usersetting = UserSetting(user=user)
                            if is_policy:
                                usersetting.balance = [
                                    dict(
                                        id=le.leave_type_id,
                                        available=le.entitlement,
                                        consumed=0,
                                        has_indefinite=le.leave_type.has_indefinite,
                                    )
                                    for le in LeaveTypePolicyRef.objects.select_related(
                                        "leave_type"
                                    )
                                    .filter(leave_policy_id=field["value"])
                                    .iterator()
                                ]
                            else:
                                usersetting.leave_process_id = (
                                    field["value"] or None
                                )
                            usersetting.save()
                        else:
                            orgin_data = getattr(usersetting, fd_name)
                            if orgin_data != field["value"]:
                                if is_policy:
                                    if (
                                        user.apply_leaves.filter(
                                            has_finish=False
                                        ).exists()
                                        | user.apply_carryovers.filter(
                                            has_finish=False
                                        ).exists()
                                    ):
                                        return cls(
                                            status=False,
                                            message=cls.WARNNING_MSG,
                                        )
                                    org_balances = usersetting.balance
                                    if field["value"]:
                                        new_balances = list()
                                        for balance in (
                                            LeaveTypePolicyRef.objects.select_related(
                                                "leave_type"
                                            )
                                            .filter(
                                                leave_policy_id=field["value"]
                                            )
                                            .iterator()
                                        ):
                                            new_balances.append(
                                                dict(
                                                    available=balance.entitlement,
                                                    consumed=0,
                                                    id=balance.leave_type_id,
                                                    has_indefinite=balance.leave_type.has_indefinite,
                                                )
                                            )
                                        if not usersetting.has_updated_policy:
                                            new_balances.extend(
                                                [
                                                    {
                                                        **org_balance,
                                                        "type": BALANCE_TYPE_P,
                                                    }
                                                    for org_balance in org_balances
                                                ]
                                            )
                                        else:
                                            new_balances.extend(
                                                [
                                                    org_balance
                                                    for org_balance in org_balances
                                                    if BALANCE_TYPE_P
                                                    == org_balance.get("type")
                                                ]
                                            )
                                        if usersetting.leave_policy_id:
                                            usersetting.has_updated_policy = (
                                                True
                                            )
                                            user.apply_carryovers.all().delete()
                                        usersetting.balance = new_balances
                                        usersetting.leave_policy_id = field[
                                            "value"
                                        ]
                                    else:
                                        # clear
                                        usersetting.balance = ""
                                        usersetting.leave_policy_id = None
                                else:
                                    usersetting.leave_process_id = field[
                                        "value"
                                    ]
                                usersetting.save()
            if user:
                user.save()
        except ObjectDoesNotExist:
            return cls(status=False, message=ErrMsg.NODATAMSG)
        except KeyError:
            return cls(status=False, message=ErrMsg.REQERRMSG)
        return cls(status=True)
