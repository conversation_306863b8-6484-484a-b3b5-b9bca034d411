# from model_bakery import baker

# from utils.basetestcase import BaseGraphQLTestCase
# from utils.constant import ROLE


# class PartnerTest(BaseGraphQLTestCase):
#     def setUp(self):
#         self.provider = self.mock_provider()
#         self.user = self.mock_user(role_id=ROLE.ROOT.value)
#         self.partner = self.baker_make('adm.PayrollPartner',
#                                        name='TEST_PARTNER',
#                                        provider_id=self.provider.id)

#     def test_crud(self):
#         self.login_with_model(self.user)

#         content = self.gql_query(
#             '''
#             mutation addPartnerUser($data: AddPartnerUserInput!) {
#                 addPartnerUser(data: $data) {
#                     status
#                     message
#                 }
#             }
#             ''',
#             operation_name='addPartnerUser',
#             variables={
#                 "data": {
#                     "partnerId": self.partner.id,
#                     "firstname": "partner-u-f",
#                     "lastname": "partner-u-l",
#                     "email": "<EMAIL>"
#                 }
#             }
#         )
#         self.assertEqual(content['addPartnerUser']['status'], True)

#         content = self.gql_query(
#             '''
#             query partnerUserList($workFor: Int) {
#                 partnerUserList(workFor: $workFor) {
#                     totalCount
#                     results {
#                         id
#                         fullName
#                         email
#                     }
#                 }
#             }
#             ''',
#             operation_name='partnerUserList',
#             variables={
#                 "workFor": self.partner.id,
#             }
#         )
#         self.assertEqual(content['partnerUserList']['totalCount'], 1)
#         self.assertEqual(content['partnerUserList']['results'][0]['email'], '<EMAIL>')

#         self.logout()
