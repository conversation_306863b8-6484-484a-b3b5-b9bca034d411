from rest_framework import serializers
from rest_framework.validators import UniqueValidator

from adm.models import Area, Industry
from user.models import User, UserProfile
from utils.basefield import ChoicesField, ModelField
from utils.constant import (
    COMPANY_SIZE,
    ROLE,
    VIRTUAL_LOCAL_ENTITY,
    VIRTUAL_PROVIDER,
    VIRTUAL_TEAM,
)
from utils.djangoplus import validate_password


class UserProfileSerializers(serializers.ModelSerializer):
    company_name = serializers.CharField(required=True)
    industry = ModelField(model=Industry, required=False)
    company_size = ChoicesField(choices=COMPANY_SIZE, required=False)

    class Meta:
        model = UserProfile
        exclude = (
            "has_delete",
            "updated_at",
            "created_at",
            "_company_name_data",
        )


class RegisterUserSerializers(serializers.ModelSerializer):
    confirm_password = serializers.CharField(required=True, write_only=True)
    location_country = ModelField(
        model=Area, fields=["name_en"], read_only=True
    )
    location_city = ModelField(
        model=Area, fields=["id", "name_en"], required=True
    )
    user_profile = UserProfileSerializers(required=True)
    email = serializers.EmailField(
        validators=[
            UniqueValidator(
                queryset=User.objects.all(), message="Email must be unique."
            )
        ]
    )

    # Register user
    REGISTER_ROLE = ROLE.REGISTERED_USER.value

    def to_representation(self, _):
        return {}

    def validate(self, attrs):
        city = attrs["location_city"]
        attrs.update(location_country=city.country)
        return attrs

    def create(self, validated_data):
        confirm_password = validated_data.pop("confirm_password")
        password = validated_data["password"]
        if password != confirm_password:
            raise serializers.ValidationError(
                "The password is not the same as the confirmed password."
            )
        # validate password
        validate_password(password)
        user_profile_info = validated_data.pop("user_profile")
        # add default
        validated_data.update(
            team_id=VIRTUAL_TEAM,
            provider_id=VIRTUAL_PROVIDER,
            company_id=VIRTUAL_LOCAL_ENTITY,
            role_id=self.REGISTER_ROLE,
            email=validated_data["email"].lower(),
        )
        user = User.objects.create(**validated_data)
        user_profile = UserProfile.objects.create(**user_profile_info)
        user.user_profile = user_profile
        user.set_password(password)
        user.save()
        return user

    class Meta:
        model = User
        fields = (
            "email",
            "lastname",
            "firstname",
            "user_profile",
            "location_city",
            "location_country",
            "confirm_password",
            "password",
        )
        write_only_fields = ("password",)
        read_only_fields = ("role",)
