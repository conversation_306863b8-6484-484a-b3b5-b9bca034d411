from django.core.management.base import BaseCommand

from user.models import User


class Command(BaseCommand):
    help = "convert email to lower case"

    def handle(self, *args, **kwargs):
        users = User.objects.exclude(onboarding_status=0)
        change_lst = []
        for user in users:
            email = user.email.lower()
            if user.email != email:
                change_lst.append(f"{user.email}->{email}")
                user.email = email
                user._email_data = email
                user.save()
        if change_lst:
            print("change emails lst ", change_lst)
