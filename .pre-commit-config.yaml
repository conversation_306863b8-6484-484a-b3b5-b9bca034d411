ci:
  autofix_prs: false

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
        exclude: ^.*\.egg-info/
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-json
      - id: check-toml
      - id: check-yaml
      - id: pretty-format-json
        args: [--autofix, --no-ensure-ascii, --no-sort-keys]
      - id: check-ast
      - id: debug-statements

  - repo: https://github.com/psf/black
    rev: 22.8.0
    hooks:
      - id: black
        args: [--line-length=79]

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black", --line-length=79]

  - repo: https://github.com/pycqa/flake8
    rev: 5.0.4
    hooks:
      - id: flake8
        args: ["--exclude=*/migrations,*/tests.py,*/tests","--ignore=E203,E266,E501,W503,W504","--extend-ignore=F403,F405,F401,E128,E124,C901,E731,F821"]

  - repo: https://github.com/pre-commit/pre-commit
    rev: v2.20.0
    hooks:
      - id: validate_manifest
