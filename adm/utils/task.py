from typing import Optional, Union

from django.db.models import Q
from django.utils import timezone

from adm.models import PayrollPartnerSpecialist, Team
from pay.models import STATUS_DRAFT, STATUS_NOT, PayrollReport, ServiceFee
from user.models import User, UserPlatformSetting
from utils.constant import INVOICE_NOT_STARTED
from utils.cryptography import filter_encrypt_users


class TaskInvoiceStatus:
    Empty = 1
    Draft = 2
    Sent = 3
    EmptyAndDraft = 4


class TaskClientPayrollStatus:
    # 1:Not started, 2:In progress, 3:Completed
    NOT_STARTED = 1
    IN_PROGRESS = 2
    COMPLETED = 3


def get_task_client_list(
    current_user: User,
    id: Optional[Union[int, str]] = None,
    name: Optional[str] = None,
    biller: Optional[Union[int, str]] = None,
    account_manager: Optional[Union[int, str]] = None,
    invoice_status: Optional[int] = None,
    payroll_status: Optional[int] = None,
    service_fee_status: Optional[int] = None,
    year: Optional[int] = None,
    month: Optional[int] = None,
):
    now = timezone.now()
    year = year or now.year
    month = month or now.month

    payroll_report_client_set = set(
        PayrollReport.objects.filter(
            category=PayrollReport.CATEGORY_INVOICE_PAYROLL,
            year=year,
            month=month,
            team__phase=Team.ACTIVE_PHASE,
        ).values_list("team_id", flat=True)
    )
    service_fee_client_set = set(
        ServiceFee.objects.filter(
            year=year,
            month=month,
            team__phase=Team.ACTIVE_PHASE,
        ).values_list("team_id", flat=True)
    )
    combined_client_id_set = payroll_report_client_set | service_fee_client_set
    queryset = Team.objects.filter(id__in=combined_client_id_set)

    if id:
        queryset = queryset.filter(id=id)

    if account_manager:
        queryset = queryset.filter(am_id=account_manager)

    if name:
        queryset = queryset.filter_by_encrypted_field(name, lookup="icontain")

    if biller:
        queryset = queryset.filter(biller=biller)

    if payroll_status:
        clients_id = list()
        payroll_clients = queryset.filter(id__in=payroll_report_client_set)
        payroll_checker = Team.PAYROLL_CHECKER_MAP[payroll_status]
        for client in payroll_clients:
            if payroll_checker(client.payroll_reports.all()):
                clients_id.append(client.id)

        queryset = queryset.filter(id__in=clients_id)

    if service_fee_status:
        client_ids = list()
        service_fee_clients = queryset.filter(id__in=service_fee_client_set)
        service_fee_checker = Team.SF_CHECKER_MAP[service_fee_status]
        for client in service_fee_clients:
            if service_fee_checker(client.service_fees.all()):
                client_ids.append(client.id)

        queryset = queryset.filter(id__in=client_ids)

    if invoice_status:
        client_ids = list()
        clients = queryset
        invoice_checker = Team.IN_CHECKER_MAP[invoice_status]
        for client in clients:
            if invoice_checker(
                client.service_fees.filter(year=year, month=month),
                client.payroll_reports.filter(year=year, month=month),
            ):
                client_ids.append(client.id)

        if invoice_status == INVOICE_NOT_STARTED:
            q = Q(service_fees__isnull=True) | Q(payroll_reports__isnull=True)
            queryset = queryset.filter(Q(id__in=client_ids) | q).distinct()
        else:
            queryset = queryset.filter(id__in=client_ids)

    return queryset


def get_task_employee_list(
    current_user,
    id: Optional[Union[int, str]] = None,
    client: Optional[Union[int, str]] = None,
    account_manager: Optional[Union[int, str]] = None,
    payroll_specialist: Optional[Union[int, str]] = None,
    country: Optional[Union[int, str]] = None,
    partner: Optional[Union[int, str]] = None,
    name: Optional[str] = None,
    payroll_status: Optional[int] = None,
    service_fee_status: Optional[int] = None,
    year: Optional[int] = None,
    month: Optional[int] = None,
):
    now = timezone.now()
    year = year or now.year
    month = month or now.month

    payroll_report_user_set = PayrollReport.objects.filter(
        year=year,
        month=month,
        category=PayrollReport.CATEGORY_INVOICE_PAYROLL,
        info_status__in=[STATUS_DRAFT, STATUS_NOT],
        user__phase__in=[User.ACTIVE_PHASE, User.UPCOMING_PHASE],
    ).values_list("user_id", flat=True)

    service_fee_user_set = ServiceFee.objects.filter(
        year=year,
        month=month,
        info_status__in=[STATUS_DRAFT, STATUS_NOT],
        user__phase__in=[User.ACTIVE_PHASE, User.UPCOMING_PHASE],
    ).values_list("user_id", flat=True)

    combined_user_id_set = set(payroll_report_user_set) | set(
        service_fee_user_set
    )
    queryset = User.objects.filter(id__in=combined_user_id_set)

    if id:
        queryset = queryset.filter(id=id)

    if client:
        queryset = queryset.filter(work_for=client)

    if country:
        queryset = queryset.filter(location_country_id=country)

    if account_manager:
        user_ids = UserPlatformSetting.objects.filter(
            am_id=account_manager
        ).values_list("user_id", flat=True)
        queryset = queryset.filter(pk__in=user_ids)

    if payroll_specialist:
        partner_ids = PayrollPartnerSpecialist.objects.filter(
            specialist=payroll_specialist,
            specialist_type=PayrollPartnerSpecialist.TYPE_PAYROLL,
        ).values_list("payroll_partner_id", flat=True)
        user_ids = UserPlatformSetting.objects.filter(
            payroll_partner_id__in=partner_ids
        ).values_list("user_id", flat=True)
        queryset = queryset.filter(pk__in=user_ids)

    if partner:
        user_ids = UserPlatformSetting.objects.filter(
            payroll_partner_id=partner
        ).values_list("user_id", flat=True)
        queryset = queryset.filter(pk__in=user_ids)

    if name:
        queryset = filter_encrypt_users(name, queryset, queryset)

    if payroll_status is not None:
        report_users = PayrollReport.objects.filter(
            category=PayrollReport.CATEGORY_INVOICE_PAYROLL,
            year=year,
            month=month,
            info_status=payroll_status,
        ).values_list("user_id", flat=True)
        if payroll_status == STATUS_NOT:
            queryset = queryset.filter(
                Q(pk__in=report_users) | Q(payroll_reports__isnull=True)
            ).distinct()
        else:
            queryset = queryset.filter(pk__in=report_users)

    if service_fee_status is not None:
        fee_users = ServiceFee.objects.filter(
            year=year,
            month=month,
            info_status=service_fee_status,
        ).values_list("user_id", flat=True)
        if service_fee_status == STATUS_NOT:
            queryset = queryset.filter(
                Q(pk__in=fee_users) | Q(service_fees__isnull=True)
            ).distinct()
        else:
            queryset = queryset.filter(pk__in=fee_users)

    return queryset
