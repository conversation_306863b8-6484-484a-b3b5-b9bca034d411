from adm.models import ProviderService, ServicesFee, ServicesRootTemplate
from pay.models import PayTemplate
from pay.views import salary_calculator
from user.models import User
from utils.constant import PAY


class CalculateFee:
    def __init__(
        self,
        salary=1.00,
        user_id=None,
        city_id=None,
        nationality=None,
        hiring_type=None,
    ):
        self.salary = salary or 1.00

        # Dependency of computing payroll cost
        self.user_id = user_id
        self.city_id = city_id
        self.nationality = nationality or ProviderService.ORIGIN_LOCAL
        self.hiring_type = hiring_type or PAY.MONTHLY

        self.payroll_cost = self.salary
        self.payroll_cost_calculated = False

    def calculate_payroll_cost(self):
        try:
            salary = round(
                self.salary / PAY.HIRE_TYPE_MAP[int(self.hiring_type)], 2
            )
            user_id = self.user_id
            # template params
            base_salary_id = "1000"
            default_payroll_year = 2020
            default_payroll_month = 1

            template = PayTemplate.objects.filter(
                template_type=PAY.SIMULATION,
                template_status=PAY.PUBLISHED,
                location_id=self.city_id,
                apply_to=self.nationality,
            )
            if not template.exists():
                self.payroll_cost = 0.0
                self.payroll_cost_calculated = True
                return

            template = template.get()
            payroll_items = template.payroll_items
            special_deductions = template.special_deductions

            [
                b.update(
                    value=float(salary) if b["id"] == base_salary_id else 0
                )
                for a in payroll_items
                for b in a["list"]
            ]
            [
                c["amount"].update(value=0)
                for a in special_deductions
                for b in a["list"]
                for c in b["taxes"]["metrics"]
                if c["amount"]["selectValue"] == "3"
            ]
            calc_result = salary_calculator(
                salary,
                0,
                template,
                user_id,
                default_payroll_year,
                special_deductions,
                payroll_items,
                [],
                default_payroll_month,
            )

            base_salary = calc_result.get("base_salary", 0)
            mandatory_benefits_er = calc_result.get("mandatory_benefits_er", 0)
            payroll_cost = base_salary + mandatory_benefits_er

            self.payroll_cost_calculated = True
            self.payroll_cost = payroll_cost
            # print('calculate_payroll_cost', base_salary, mandatory_benefits_er, payroll_cost)
        except Exception as e:
            print("CalculateFee calculate_payroll_cost", e)
            self.payroll_cost = 0.0
            self.payroll_cost_calculated = True

    def calculate_fee(
        self,
        category: ServicesFee.CATEGORY_CHOICES,
        fee_value: float,
        is_month=False,
    ):
        if fee_value is None:
            return None

        fee_value = float(fee_value)
        fee_value_percent = fee_value / 100

        if category == ServicesFee.CATEGORY_PERCENTAGE:
            fee_value = fee_value_percent * self.salary
        elif category == ServicesFee.CATEGORY_PERCENTAGE_PAYROLL_COST:
            if not self.payroll_cost_calculated:
                self.calculate_payroll_cost()
            # print(self.salary, fee_value_percent, self.payroll_cost, fee_value_percent * self.payroll_cost)
            fee_value = fee_value_percent * self.payroll_cost

        return fee_value / 12 if is_month else fee_value

    def calculate_service_fee(self, f: ServicesFee) -> float:
        one_time = (
            self.calculate_fee(f.category, f.one_time, is_month=True) or 0.0
        )
        annual = self.calculate_fee(f.category, f.annual, is_month=True) or 0.0
        monthly = self.calculate_fee(f.category, f.monthly) or 0.0
        return one_time + annual + monthly

    def calculate_service_fees(self, fees: [ServicesFee]):
        cats = [
            ServicesFee.CATEGORY_FIXED_AMOUNT,
            ServicesFee.CATEGORY_PERCENTAGE,
            ServicesFee.CATEGORY_PERCENTAGE_PAYROLL_COST,
        ]
        service_fees = 0
        supplemental_charges = 0

        service_fees_queryset = fees.filter(
            parent__fee_kind=ServicesRootTemplate.FEE_KIND_SERVICE_FEE,
            category__in=cats,
        )
        for f in list(service_fees_queryset):
            service_fees += self.calculate_service_fee(f)

        supplemental_charges_queryset = fees.filter(
            parent__fee_kind=ServicesRootTemplate.FEE_KIND_SUPPLEMENTAL_CHARGES,
            category__in=cats,
        )
        for f in list(supplemental_charges_queryset):
            supplemental_charges += self.calculate_service_fee(f)

        return service_fees, supplemental_charges


def get_recommended_provider_service(services, salary):
    user = User.objects.first()
    cf = CalculateFee(salary=salary, user_id=user.id, hiring_type=PAY.MONTHLY)
    result = []

    for s in list(services):
        cf.nationality = s.origin
        cf.city_id = s.city_id
        sf, sc = cf.calculate_service_fees(s.fees.all())
        # force recalculate
        cf.payroll_cost_calculated = False
        result.append({"id": s.id, "model": s, "total": sf + sc})

    result.sort(key=lambda x: x["total"])
    return result[0]["model"] if result else None


def check_fee_isvalid(fee: ServicesFee) -> bool:
    if fee.category == ServicesFee.CATEGORY_FREE_OF_CHARGE:
        return True
    if fee.name == "":
        return False
    if fee.one_time is None and fee.monthly is None and fee.annual is None:
        return False
    return True


def check_fees_isvalid(service_id):
    error_node = []
    fees = ServicesFee.objects.filter(service_id=service_id).exclude(
        category=ServicesFee.CATEGORY_FREE_OF_CHARGE
    )
    for f in list(fees):
        if not check_fee_isvalid(f):
            error_node.append(f.id)

    return error_node, "The template is not completed. Please check."
