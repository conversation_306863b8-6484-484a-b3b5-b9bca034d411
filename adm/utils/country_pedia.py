from adm.models import CountryAllowance


def validate_country_allowance_args(min_amount, max_amount, contractual):
    if max_amount:
        if (
            min_amount < 0
            or max_amount < 0
            or contractual not in CountryAllowance.CONTRACTUAL_ENUM
        ):
            return False
        return False if max_amount < min_amount else True
    else:
        if (
            min_amount < 0
            or contractual not in CountryAllowance.CONTRACTUAL_ENUM
        ):
            return False
    return True
