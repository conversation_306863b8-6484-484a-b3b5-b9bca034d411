# Generated by Django 3.1 on 2022-09-05 07:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("adm", "0040_auto_20220825_0735"),
    ]

    operations = [
        migrations.CreateModel(
            name="XeroSyncLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("has_delete", models.BooleanField(default=False)),
                ("batch_id", models.CharField(max_length=255)),
                ("reference", models.<PERSON>r<PERSON><PERSON>(max_length=50, db_index=True)),
                ("request", models.TextField()),
                ("response", models.TextField()),
                ("has_errors", models.BooleanField()),
                ("errors", models.TextField(blank=True)),
                ("validation_errors", models.TextField(blank=True)),
                ("warnings", models.TextField(blank=True)),
            ],
            options={
                "ordering": ["-id"],
                "abstract": False,
            },
        )
    ]
