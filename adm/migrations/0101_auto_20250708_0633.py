# Generated by Django 3.1.13 on 2025-07-08 06:33

from django.db import migrations, models


def add_service_fees(apps, schema_editor):
    late_fee_name = "Late Payment Fee"
    bank_fee_name = "Bank Fee Recharge"

    # Use raw SQL to avoid ORM issues with new fields not yet in database
    with schema_editor.connection.cursor() as cursor:
        # Check if Late Payment Fee already exists
        cursor.execute("""
            SELECT COUNT(*) FROM adm_company_service_fee
            WHERE name = %s AND billing_class = %s AND fee_status = %s AND has_delete = %s
        """, [late_fee_name, "ENTITY", "ACTIVE", False])

        if cursor.fetchone()[0] == 0:
            # Insert Late Payment Fee
            cursor.execute("""
                INSERT INTO adm_company_service_fee
                (name, billing_class, charge_type, fee_status, xero_account_code, created_at, updated_at, has_delete)
                VALUES (%s, %s, %s, %s, %s, NOW(), NOW(), %s)
            """, [late_fee_name, "ENTITY", "ADD_ON", "ACTIVE", "", False])

        # Check if Bank Fee Recharge already exists
        cursor.execute("""
            SELECT COUNT(*) FROM adm_company_service_fee
            WHERE name = %s AND billing_class = %s AND fee_status = %s AND has_delete = %s
        """, [bank_fee_name, "ENTITY", "ACTIVE", False])

        if cursor.fetchone()[0] == 0:
            # Insert Bank Fee Recharge
            cursor.execute("""
                INSERT INTO adm_company_service_fee
                (name, billing_class, charge_type, fee_status, xero_account_code, created_at, updated_at, has_delete)
                VALUES (%s, %s, %s, %s, %s, NOW(), NOW(), %s)
            """, [bank_fee_name, "ENTITY", "ADD_ON", "ACTIVE", "", False])


class Migration(migrations.Migration):
    dependencies = [
        ("adm", "0100_auto_20250708_0418"),
    ]

    operations = [
        migrations.AddField(
            model_name="invoicereport",
            name="legal_entity_id",
            field=models.IntegerField(null=True),
        ),
        migrations.RunPython(
            add_service_fees,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
