# Generated by Django 3.1.13 on 2025-07-08 06:33

from auditlog.registry import auditlog
from django.db import migrations, models


def add_service_fees(apps, schema_editor):
    from adm.models import CompanyServiceFee

    auditlog.unregister(CompanyServiceFee)

    late_fee_name = "Late Payment Fee"
    bank_fee_name = "Bank Fee Recharge"

    late_fee = CompanyServiceFee.objects.filter(
        name=late_fee_name,
        billing_class=CompanyServiceFee.BILLING_CLASSES.ENTITY,
        fee_status=CompanyServiceFee.STATUS.ACTIVE,
    ).first()
    if not late_fee:
        late_fee = CompanyServiceFee(
            name=late_fee_name,
            billing_class=CompanyServiceFee.BILLING_CLASSES.ENTITY,
            charge_type=CompanyServiceFee.CHARGE_TYPES.ADD_ON,
            fee_status=CompanyServiceFee.STATUS.ACTIVE,
            xero_account_code="",
        )
        late_fee.save()

    bank_fee = CompanyServiceFee.objects.filter(
        name=bank_fee_name,
        billing_class=CompanyServiceFee.BILLING_CLASSES.ENTITY,
        fee_status=CompanyServiceFee.STATUS.ACTIVE,
    ).first()
    if not bank_fee:
        bank_fee = CompanyServiceFee(
            name=bank_fee_name,
            billing_class=CompanyServiceFee.BILLING_CLASSES.ENTITY,
            charge_type=CompanyServiceFee.CHARGE_TYPES.ADD_ON,
            fee_status=CompanyServiceFee.STATUS.ACTIVE,
            xero_account_code="",
        )
        bank_fee.save()

    with schema_editor.connection.cursor() as cursor:
        late_fee_name = "Late Payment Fee"
        bank_fee_name = "Bank Fee Recharge"
        billing_class = CompanyServiceFee.BILLING_CLASSES.ENTITY
        charge_type = CompanyServiceFee.CHARGE_TYPES.ADD_ON
        fee_status = CompanyServiceFee.STATUS.ACTIVE

        # Insert Late Payment Fee if not exists
        cursor.execute("""
            INSERT INTO adm_companyservicefee (name, billing_class, charge_type, fee_status, xero_account_code)
            SELECT %s, %s, %s, %s, %s
            WHERE NOT EXISTS (
                SELECT 1 FROM adm_companyservicefee
                WHERE name=%s AND billing_class=%s AND fee_status=%s
            )
        """, [late_fee_name, billing_class, charge_type, fee_status, "", late_fee_name, billing_class, fee_status])

        # Insert Bank Fee Recharge if not exists
        cursor.execute("""
            INSERT INTO adm_companyservicefee (name, billing_class, charge_type, fee_status, xero_account_code)
            SELECT %s, %s, %s, %s, %s
            WHERE NOT EXISTS (
                SELECT 1 FROM adm_companyservicefee
                WHERE name=%s AND billing_class=%s AND fee_status=%s
            )
        """, [bank_fee_name, billing_class, charge_type, fee_status, "", bank_fee_name, billing_class, fee_status])

class Migration(migrations.Migration):

    dependencies = [
        ("adm", "0100_auto_20250708_0418"),
    ]

    operations = [
        migrations.AddField(
            model_name="invoicereport",
            name="legal_entity_id",
            field=models.IntegerField(null=True),
        ),
        migrations.RunPython(
            add_service_fees,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
