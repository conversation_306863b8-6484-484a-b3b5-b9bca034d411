import csv
import io
import os
import random
import shutil
from typing import List, <PERSON>ple

import pdfkit
from django.template import loader
from django.utils import timezone

from adm.models import ProviderProfile, Team
from expenses.models import (
    Expense,
    ExpenseAttachment,
    ExpenseHistory,
    ExpenseReport,
)
from expenses.utils.history import get_history_operator_info, get_history_time
from management.celery import get_provider_template_content
from management.settings import WKHTMLTOPDF_PATH
from user.models import User
from utils.constant import (
    PDF_TEMPLATE_PATH,
    get_category_name,
    get_currency_symbol,
)
from utils.file import (
    copy_files_to_dir,
    get_export_dir_path,
    get_export_file_path,
    get_local_media_url,
    get_rocketg_media_url,
)
from utils.pdf import export_image_pdf, merge_pdfs

EXPORT_REPORT_TEMPLATE = os.path.join(PDF_TEMPLATE_PATH, "export_report.html")
EXPORT_FILE_PREFIX = "er-"

REPORT_PDF_OPTIONS = {
    # html 中 px 尺寸 * 1.7
    # 595px * 842px => 1012px * 1431px
    "page-size": "A4",
    "page-width": "595px",
    "page-height": "842px",
    "dpi": 75,
    "margin-top": "27px",
    "margin-right": "40px",
    "margin-bottom": "1px",
    "margin-left": "40px",
    "encoding": "UTF-8",
}


class ExportFileUtils:
    @staticmethod
    def export_report_csv(
        reports: List[ExpenseReport],
    ) -> Tuple[str, str, str]:
        """
        :param reports: report instance list
        :return: (pdf file path, file name)
        """
        file_path = get_export_file_path(ext="csv", prefix=EXPORT_FILE_PREFIX)
        reports_data, file_name = ExportFileUtils._get_csv_reports_data(
            reports
        )

        with open(
            file_path, "w", newline="", encoding="utf-8-sig"
        ) as csv_file:
            writer = csv.writer(csv_file)
            writer.writerow(
                [
                    "Report name",
                    "Submitter",
                    "Merchant",
                    "Description",
                    "Receipt date",
                    "Amount",
                    "Payout due",
                    "Category",
                    "Total",
                    "FX Rate",
                ]
            )

            for report in reports_data:
                expenses = report.get("expenses", [])
                for expense in expenses:
                    writer.writerow(
                        [
                            report.get("title"),
                            report.get("submitter"),
                            expense.get("merchant"),
                            expense.get("desc"),
                            expense.get("receipt_date"),
                            expense.get("currency") + expense.get("total"),
                            report.get("payment_due"),
                            expense.get("category"),
                            report.get("currency") + report.get("total"),
                            expense.get("rate"),
                        ]
                    )

        return file_path, "%s.csv" % file_name, ""

    @staticmethod
    def export_split_report_pdf(
        reports: List[ExpenseReport], tz=None, has_attachments=True
    ) -> Tuple[str, str, str]:
        """
        :param reports: report instance list
        :param tz: timezone
        :param has_attachments: export attachments
        :return: (pdf file path, file name)
        """
        if not has_attachments:
            return ExportFileUtils.export_report_pdf(
                reports=reports, tz=tz, has_attachments=has_attachments
            )

        pdf_list = []
        file_path = get_export_file_path(prefix=EXPORT_FILE_PREFIX)
        reports_data, file_name, _, _ = ExportFileUtils._get_pdf_reports_data(
            reports, tz
        )

        for report in reports_data:
            pdf_list.append(ExportFileUtils._get_reports_pdf([report]))
            other_imgs = report.get("imgs")
            other_pdfs = report.get("pdfs")

            if other_imgs:
                pdf_list.append(ExportFileUtils._get_img_pdf(other_imgs))
            if other_pdfs:
                pdf_list += other_pdfs

        merge_pdfs(pdf_list, file_path)
        return file_path, "%s.pdf" % file_name, ""

    @staticmethod
    def export_report_zip(
        reports: List[ExpenseReport], tz=None
    ) -> Tuple[str, str, str]:
        """
        :param reports: report instance list
        :param tz: timezone
        :return: (pdf file path, file name)
        """
        zip_name = ExportFileUtils._get_multiple_reports_file_name()
        zip_dir_path = get_export_dir_path(prefix=EXPORT_FILE_PREFIX + "zip-")

        for report in reports:
            (
                report_data,
                file_name,
                _,
                attachment_name_map,
            ) = ExportFileUtils._get_pdf_reports_data([report], tz)
            for item in report_data:
                item_file_name = "%s-%s" % (
                    random.randint(10000, 99999),
                    file_name,
                )
                item_file_path = "%s/%s.pdf" % (zip_dir_path, item_file_name)
                pdf_list = [ExportFileUtils._get_reports_pdf([item])]
                other_imgs = item.get("imgs")
                other_pdfs = item.get("pdfs")
                all_files = []
                if other_imgs:
                    pdf_list.append(ExportFileUtils._get_img_pdf(other_imgs))
                    all_files += other_imgs
                if other_pdfs:
                    pdf_list += other_pdfs
                    all_files += other_pdfs

                try:
                    merge_pdfs(pdf_list, item_file_path)
                except Exception as e:
                    # FIX: https://gitlab.com/rocketg/horizons/product/-/issues/2049
                    print("Report(%s) merge error: %s" % (report.id, e))
                    if os.path.exists(item_file_path):
                        os.remove(item_file_path)

                    try:
                        copydirname = "%s/%s" % (zip_dir_path, item_file_name)
                        copy_files_to_dir(
                            all_files, copydirname, attachment_name_map
                        )

                        report_pdf_path = "%s.pdf" % file_name
                        ExportFileUtils._get_reports_pdf(
                            [item], "%s/%s" % (copydirname, report_pdf_path)
                        )
                    except Exception as e:
                        return (
                            "",
                            "",
                            "Report(%s) Export failure: %s" % (report.id, e),
                        )

        shutil.make_archive(zip_dir_path, "zip", zip_dir_path)
        shutil.rmtree(zip_dir_path)
        return "%s.zip" % zip_dir_path, "%s.zip" % zip_name, ""

    @staticmethod
    def export_report_pdf(
        reports: List[ExpenseReport], tz=None, has_attachments=True
    ) -> Tuple[str, str, str]:
        """
        :param reports: report instance list
        :param tz: timezone
        :param has_attachments: export attachments
        :return: (pdf file path, file name)
        """
        pdf_list = []
        file_path = get_export_file_path(prefix=EXPORT_FILE_PREFIX)

        (
            reports_data,
            file_name,
            attachments,
            _,
        ) = ExportFileUtils._get_pdf_reports_data(reports, tz)
        pdf_list.append(ExportFileUtils._get_reports_pdf(reports_data))

        if has_attachments:
            if attachments.get("img"):
                pdf_list.append(
                    ExportFileUtils._get_img_pdf(attachments.get("img"))
                )
            if attachments.get("pdf"):
                pdf_list += attachments.get("pdf")

        merge_pdfs(pdf_list, file_path)
        return file_path, "%s.pdf" % file_name, ""

    @staticmethod
    def _get_csv_reports_data(
        reports: List[ExpenseReport],
    ) -> Tuple[List[dict], str]:
        result = []
        file_name = ""

        for report in reports:
            title, submitter = ExportFileUtils._get_report_title_and_submitter(
                report
            )
            file_name = title
            expenses = list(report.expenses.all().order_by("id"))

            data = dict(
                title=title,
                submitter=submitter,
                payment_due=report.payment_due,
                total="%.2f" % sum(e.converted_amount for e in expenses),
                currency=get_currency_symbol(report.payments_currency),
                expenses=ExportFileUtils._get_expenses_data(expenses),
            )

            result.append(data)

        if len(reports) > 1:
            file_name = ExportFileUtils._get_multiple_reports_file_name()

        return result, file_name

    @staticmethod
    def _get_pdf_reports_data(
        reports: List[ExpenseReport], tz=None
    ) -> Tuple[List[dict], str, dict]:
        result = []
        file_name = ""
        attachments = dict(img=[], pdf=[])

        for report in reports:
            title, submitter = ExportFileUtils._get_report_title_and_submitter(
                report
            )
            file_name = title
            report_total = format(
                sum(
                    report.expenses.values_list("converted_amount", flat=True)
                ),
                ",.2f",
            )
            expenses = report.expenses.all().order_by("id")

            data = dict(
                report=dict(
                    title=title,
                    submitter=submitter,
                    currency=get_currency_symbol(report.payments_currency),
                    total=report_total,
                    last_approver=ExportFileUtils._get_last_approver(report),
                    current_approver=ExportFileUtils._get_current_approver(
                        report
                    ),
                    action_name=ExportFileUtils._get_action_name(report),
                ),
                expenses=ExportFileUtils._get_expenses_data(expenses),
                latest_expense_logs=ExportFileUtils._get_expense_logs(
                    expenses.first(), tz
                ),
            )

            (
                imgs,
                pdfs,
                attachments_name_map,
            ) = ExportFileUtils._get_expenses_attachments(expenses)
            data.update(imgs=imgs)
            data.update(pdfs=pdfs)
            attachments["img"] += imgs
            attachments["pdf"] += pdfs

            result.append(data)

        if len(reports) > 1:
            file_name = ExportFileUtils._get_multiple_reports_file_name()

        file_name = file_name.replace("/", "_")
        return result, file_name, attachments, attachments_name_map

    @staticmethod
    def _get_reports_pdf(reports_data: List[dict], output_path=None):
        config = pdfkit.configuration(wkhtmltopdf=WKHTMLTOPDF_PATH)

        report_tpl = loader.get_template(EXPORT_REPORT_TEMPLATE)
        tpl_content = get_provider_template_content("pdf")
        tpl_content.update(list=reports_data)
        report_html_content = report_tpl.render(tpl_content)
        if output_path:
            return pdfkit.from_string(
                report_html_content,
                output_path,
                configuration=config,
                options=REPORT_PDF_OPTIONS,
            )

        report_pdf_content = pdfkit.from_string(
            report_html_content,
            False,
            configuration=config,
            options=REPORT_PDF_OPTIONS,
        )
        return io.BytesIO(report_pdf_content)

    @staticmethod
    def _get_img_pdf(img_path: List[str]):
        return io.BytesIO(export_image_pdf(img_path, use_local=True))

    @staticmethod
    def _get_expenses_data(expenses: List[Expense]):
        result = []

        for expense in expenses:
            result.append(
                dict(
                    merchant=expense.name,
                    desc=expense.description,
                    receipt_date=expense.receipt_date,
                    # currency=get_currency_symbol(expense.payments_currency),
                    # total='%.2f' % expense.converted_amount,
                    currency=get_currency_symbol(
                        expense.form_payments_currency
                    ),
                    total="%.2f" % expense.amount,
                    category=get_category_name(expense.category),
                    rate=expense.rate,
                )
            )

        return result

    @staticmethod
    def _get_expenses_attachments(expenses: List[Expense]):
        attachments_img_list = []
        attachments_pdf_list = []
        name_map = dict()

        files = ExpenseAttachment.objects.filter(
            expense_id__in=expenses.values_list("id", flat=True)
        ).values("file", "file_name")

        for f in files:
            file_path = f.get("file")
            file_name = f.get("file_name")
            ext = file_path.split(".")[-1] or ""
            ext = ext.lower()
            if ext in ["png", "jpg", "jpeg"]:
                fp = get_local_media_url(file_path)
                name_map[fp] = file_name
                attachments_img_list.append(fp)
            elif ext == "pdf":
                fp = get_rocketg_media_url(file_path)
                name_map[fp] = file_name
                attachments_pdf_list.append(fp)

        return attachments_img_list, attachments_pdf_list, name_map

    @staticmethod
    def _get_report_title_and_submitter(
        report: ExpenseReport,
    ) -> Tuple[str, str]:
        submitter = report.user
        user_name = ExportFileUtils._get_user_name(submitter)
        work_for_name = ExportFileUtils._get_submitter_work_for_name(submitter)
        title = "%s_%s_%s_%s" % (
            work_for_name,
            user_name,
            report.uuid,
            report.name,
        )
        return title, user_name

    @staticmethod
    def _get_last_approver(report: ExpenseReport) -> str:
        if not report.last_approver:
            return ""
        return ExportFileUtils._get_user_name(report.last_approver)

    @staticmethod
    def _get_current_approver(report: ExpenseReport) -> str:
        if not report.process_step:
            return ""

        step = report.process_step

        if step.is_direct_report:
            if not report.user.reporter_id:
                return ""
            return ExportFileUtils._get_user_name(report.user.reporter)
        elif step.user_id:
            return ExportFileUtils._get_user_name(step.user)
        elif step.group_id:
            return step.group.name

        return ""

    @staticmethod
    def _get_action_name(report: ExpenseReport) -> str:
        if report.has_reject:
            return "Rejected"

        if not report.process_step:
            return "Draft"

        return report.process_step.action.name

    @staticmethod
    def _get_user_name(user) -> str:
        return "%s %s" % (user.firstname, user.lastname)

    @staticmethod
    def _get_submitter_work_for_name(user) -> str:
        try:
            if user.work_for_type == User.PEO:
                return ProviderProfile.objects.get(pk=user.work_for).name
            return Team.objects.get(pk=user.work_for).name
        except Exception as e:
            print(e)
            return "unknown"

    @staticmethod
    def _get_multiple_reports_file_name() -> str:
        return "Expenses Reports_%s" % timezone.now().strftime("%Y%m%d")

    @staticmethod
    def _get_expense_logs(expense: Expense, tz=None) -> List[dict]:
        result = []

        if not expense:
            return result

        history_list = ExpenseHistory.objects.filter(expense_id=expense.id)
        for h in history_list:
            info = get_history_operator_info(h)
            result.append(
                dict(
                    time=get_history_time(h, tz),
                    content=info.get("action", ""),
                )
            )

        return result
