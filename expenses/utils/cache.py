import datetime
import json

from django.core.cache import cache

from expenses.models import ExpenseReport


class TaskCacheUtils:
    @classmethod
    def create_approve_client_report_cache_key(
        cls, approver_id: int, is_admin_group=False, is_client=False
    ):
        """
        create redis key
        ea-datetime-approver_id-[is user group(0/1)]-[is client(0/1)]
        :return: ea-20210126-a110-g0-c1
        eg.
            client user approver:    ea-20210126-a110-g0-c1
            internal user approver:  ea-20210126-a110-g0-c0
            client group approver:   ea-20210126-a110-g1-c1
            internal group approver: ea-20210126-a110-g1-c0
        """
        t = datetime.datetime.now().strftime("%Y%m%d")
        is_admin_group = 1 if is_admin_group else 0
        is_client = 1 if is_client else 0
        return "ea-%s-a%d-g%d-c%d" % (
            t,
            approver_id,
            is_admin_group,
            is_client,
        )

    @classmethod
    def get_cache(cls, key: str):
        json_str = cache.get(key)
        if not json_str:
            return None

        try:
            data = json.loads(json_str)
            return data
        except Exception as e:
            print(e)
            return None

    @classmethod
    def set_cache(cls, key: str, report_id_list: [int]):
        data = {"reports": report_id_list}
        json_str = json.dumps(data)
        return cache.set(key, json_str, timeout=60 * 60 * 3)

    @classmethod
    def get_cache_reports(cls, key: str):
        cache_data = cls.get_cache(key)
        if not cache_data:
            return None
        return cache_data.get("reports")

    @classmethod
    def set_cache_reports(cls, key: str, report: ExpenseReport) -> bool:
        reports = cls.get_cache_reports(key)
        if not reports:
            cls.set_cache(key, [report.id])
            return True

        cache_reports = set(reports)
        cache_reports.add(report.id)
        cls.set_cache(key, list(cache_reports))
        return False

    @classmethod
    def del_cache(cls, key: str):
        return cache.delete(key)


class ApproveTaskLockUtils:
    """
    lock key: lock:expense-approve:task_id
    """

    @classmethod
    def _create_key(cls, task_id: str) -> str:
        return "lock:expense-approve:%s" % task_id

    @classmethod
    def acquire_lock(cls, task_id: str):
        res = cache.add(cls._create_key(task_id), True, 60 * 60 * 3)
        return res
