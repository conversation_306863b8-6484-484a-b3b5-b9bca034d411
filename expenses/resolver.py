import datetime

from django.db.models import Case, IntegerField, Q, Value, When
from django.utils import timezone

from adm.models import Area
from app.pay.models import ExpensePayment
from expenses.models import (
    ExpenseAction,
    ExpenseAttachment,
    ExpenseCurrencyRate,
    ExpenseReport,
    ExpenseStep,
)
from expenses.nodes.report import (
    ExchangeRateNode,
    ExpensePaymentListNode,
    ExpenseReportListNode,
    PartnerEmployeeExpenseReportInfoNode,
    ReportAmountInfoNode,
)
from expenses.nodes.step import ExpenseStepListNode
from expenses.utils.report import (
    get_approval_expense_report_queryset,
    get_my_expense_report_queryset,
    get_partner_employee_expense_report_queryset,
)
from user.models import User
from utils.constant import Permission
from utils.permission import permissions_checker
from utils.utils import _get_payments_currency


class ExpenseStatus:
    draft = "draft"
    pending = "pending"
    approved = "approved"
    archived = "archived"
    rejected = "rejected"


class ResolverMixin:
    @staticmethod
    def resolve_exchange_rate(
        root, info, from_payments_currency, payments_currency
    ):
        exchange_rate = ExpenseCurrencyRate.objects.filter(
            user=info.context.user,
            currency_name=payments_currency,
            currency_name_from=from_payments_currency,
        )
        if exchange_rate.exists():
            return ExchangeRateNode(
                currency_name_from=from_payments_currency,
                has_default=True,
                currency_name=payments_currency,
                rate=exchange_rate.get().rate,
            )
        return ExchangeRateNode(
            currency_name_from=from_payments_currency,
            currency_name=payments_currency,
            rate=_get_payments_currency(
                from_payments_currency, payments_currency
            )[0],
            has_default=False,
        )

    @staticmethod
    def resolve_assignees_list(root, info, report_id, only_key_step):
        if report_id:
            report = ExpenseReport.objects.get(pk=report_id)
            steps = ExpenseStep.objects.filter(process=report.expense_process)
        else:
            steps = ExpenseStep.objects.filter(
                process=info.context.user.expense_process
            )

        if only_key_step:
            steps = steps.filter(action_type=ExpenseAction.ACTION_KEY)

        if not steps.exists():
            return ExpenseStepListNode(results=[], totalCount=0)

        return ExpenseStepListNode(results=steps, totalCount=steps.count())

    @staticmethod
    @permissions_checker(
        permissions=[
            Permission.EXPENSE_INTERNAL_MY_EXPENSES,
            Permission.CLIENT_EXPENSE_MY_EXPENSES,
        ]
    )
    def resolve_my_expense_report_list(
        root, info, page, per_page, pagable, is_pending, payment_due, ordering
    ):
        user = info.context.user
        queryset = ExpenseReport.objects.all()
        queryset = get_my_expense_report_queryset(
            queryset=queryset,
            current_user=user,
            is_pending=is_pending,
            payment_due=payment_due,
        )

        if ordering:
            queryset = queryset.order_by(*ordering.split(","))

        offset = (page - 1) * per_page
        return ExpenseReportListNode(
            results=queryset[offset : offset + per_page],
            totalCount=queryset.count(),
        )

    @staticmethod
    @permissions_checker(
        permissions=[
            Permission.EXPENSE_INTERNAL_APPROVAL,
            Permission.EXPENSE_CLIENT_APPROVAL,
            Permission.CLIENT_DASHBOARD_VIEW,
            Permission.CLIENT_EXPENSE_APPROVAL,
        ]
    )
    def resolve_approval_expense_report_list(
        root,
        info,
        page,
        per_page,
        pagable,
        is_client,
        is_pending,
        payment_due,
        submitter,
        submitter_name,
        work_for,
        has_finish,
        ordering,
    ):
        queryset = get_approval_expense_report_queryset(
            queryset=ExpenseReport.objects.all(),
            current_user=info.context.user,
            is_client=is_client,
            is_pending=is_pending,
            payment_due=payment_due,
            submitter=submitter,
            submitter_name=submitter_name,
            work_for=work_for,
            has_finish=has_finish,
        )

        if ordering:
            queryset = queryset.order_by(*ordering.split(","))

        offset = (page - 1) * per_page
        return ExpenseReportListNode(
            results=queryset[offset : offset + per_page],
            totalCount=queryset.count(),
        )

    @staticmethod
    @permissions_checker(
        permissions=[Permission.CLIENT_EMPLOYEE_PROFILE_EXPENSES]
    )
    def resolve_user_expense_report_list(
        root, info, page, per_page, pagable, user_id, payment_due
    ):
        user = info.context.user
        queryset = ExpenseReport.objects.filter(
            user_id=user_id, user__team_id=user.team_id
        )

        if payment_due:
            queryset = queryset.filter(payment_due=payment_due)

        queryset = queryset.order_by(
            Case(
                When(submitted_at=None, then=Value(0)),
                default=Value(1),
                output_field=IntegerField(),
            ).asc(),
            "-submitted_at",
            "-id",
        )
        offset = (page - 1) * per_page
        return ExpenseReportListNode(
            results=queryset[offset : offset + per_page],
            totalCount=queryset.count(),
        )

    @staticmethod
    @permissions_checker(permissions=[Permission.ADMIN_CLIENTUSER_VIEW])
    def resolve_admin_expense_report_list(
        root, info, page, per_page, pagable, user_id, status, payroll_month
    ):
        queryset = ExpenseReport.objects.all()
        now = timezone.now()
        month_first_day = datetime.date(now.year, now.month, 1)

        status_filter_map = {
            ExpenseStatus.rejected: dict(has_reject=True),
            ExpenseStatus.draft: dict(
                has_reject=False, process_step_id__isnull=True
            ),
            ExpenseStatus.pending: dict(
                has_reject=False,
                has_finish=False,
                process_step_id__isnull=False,
            ),
            ExpenseStatus.approved: dict(has_reject=False, has_finish=True),
            ExpenseStatus.archived: dict(
                has_reject=False,
                has_finish=True,
                payroll_month__isnull=False,
                payroll_month__lt=month_first_day,
            ),
        }

        if user_id:
            queryset = queryset.filter(user_id=user_id)

        if status:
            queryset = queryset.filter(**status_filter_map.get(status, {}))
            if status == ExpenseStatus.approved:
                queryset = queryset.filter(
                    Q(payroll_month__gte=month_first_day)
                    | Q(payroll_month__isnull=True),
                )

        if payroll_month:
            queryset = queryset.filter(
                payroll_month__year=payroll_month.year,
                payroll_month__month=payroll_month.month,
            )

        queryset = queryset.order_by(
            Case(
                When(submitted_at=None, then=Value(0)),
                default=Value(1),
                output_field=IntegerField(),
            ).asc(),
            "-submitted_at",
            "-id",
        )

        offset = (page - 1) * per_page
        return ExpenseReportListNode(
            results=queryset[offset : offset + per_page],
            totalCount=queryset.count(),
        )

    @staticmethod
    @permissions_checker(
        permissions=[
            Permission.ADMIN_PARTNERS_FULLACCESS,
            Permission.ADMIN_PARTNERS_VIEW,
            Permission.PARTNER_DOCUMENTS_FULLACCESS,
        ]
    )
    def resolve_partner_employee_expense_report_list(
        root,
        info,
        page,
        per_page,
        pagable,
        partner_id,
        submitter_name,
        city_id,
        published,
        payroll_month,
        phases,
    ):
        queryset = get_partner_employee_expense_report_queryset(
            queryset=ExpensePayment.objects.filter(
                category=ExpensePayment.CATEGORY_INVOICE_PAYROLL,
                expense_report_id__isnull=False,
            ),
            current_user=info.context.user,
            partner_id=partner_id,
            submitter_name=submitter_name,
            city_id=city_id,
            published=published,
            payroll_month=payroll_month,
            phases=phases
        )

        # queryset = queryset.order_by("-submitted_at", "-id")
        queryset = queryset.order_by("-id")
        offset = (page - 1) * per_page
        # return ExpenseReportListNode(
        #     results=queryset[offset : offset + per_page],
        #     totalCount=queryset.count(),
        # )
        return ExpensePaymentListNode(
            results=queryset[offset : offset + per_page],
            totalCount=queryset.count(),
        )

    @staticmethod
    @permissions_checker(
        permissions=[
            Permission.ADMIN_PARTNERS_FULLACCESS,
            Permission.ADMIN_PARTNERS_VIEW,
            Permission.PARTNER_DOCUMENTS_FULLACCESS,
        ]
    )
    def resolve_partner_employee_expense_report_location_list(
        root, info, partner_id, published, payroll_month
    ):
        queryset = get_partner_employee_expense_report_queryset(
            queryset=ExpensePayment.objects.filter(
                category=ExpensePayment.CATEGORY_INVOICE_PAYROLL,
                expense_report_id__isnull=False,
            ),
            current_user=info.context.user,
            partner_id=partner_id,
            published=published,
            payroll_month=payroll_month,
        )

        city_list = queryset.values_list(
            "user__location_city_id", flat=True
        ).distinct()
        return Area.objects.filter(id__in=city_list)

    @staticmethod
    @permissions_checker(
        permissions=[
            Permission.ADMIN_PARTNERS_FULLACCESS,
            Permission.ADMIN_PARTNERS_VIEW,
        ]
    )
    def resolve_partner_employee_expense_report_Info(
        root, info, partner_id, payroll_month
    ):
        queryset = get_partner_employee_expense_report_queryset(
            queryset=ExpensePayment.objects.filter(
                category=ExpensePayment.CATEGORY_INVOICE_PAYROLL,
                expense_report_id__isnull=False,
            ),
            current_user=info.context.user,
            partner_id=partner_id,
            published=False,
            payroll_month=payroll_month,
        )

        return PartnerEmployeeExpenseReportInfoNode(
            can_publish=queryset.exists(),
        )

    @staticmethod
    def resolve_expense_report_payment_due_list(
        root, info, is_my_expense, is_client, is_pending
    ):
        user = info.context.user

        queryset = ExpenseReport.objects.filter(payment_due__isnull=False)
        queryset_params = dict(
            queryset=queryset,
            current_user=user,
            is_pending=is_pending,
        )

        if is_my_expense:
            queryset = get_my_expense_report_queryset(**queryset_params)
        else:
            queryset = get_approval_expense_report_queryset(
                **queryset_params, is_client=is_client
            )

        return sorted(
            set(queryset.values_list("payment_due", flat=True)), reverse=True
        )

    @staticmethod
    def resolve_user_expense_report_payment_due_list(root, info, user_id):
        queryset = ExpenseReport.objects.filter(
            payment_due__isnull=False, user_id=user_id
        )
        return sorted(
            set(queryset.values_list("payment_due", flat=True)), reverse=True
        )

    @staticmethod
    def resolve_expense_report_work_for_list(root, info, is_pending):
        user = info.context.user

        queryset = get_approval_expense_report_queryset(
            queryset=ExpenseReport.objects.all(),
            current_user=user,
            is_client=True,
            is_pending=is_pending,
        )
        team_type = User.STANDARD_COMPANY
        work_infos = (
            User.objects.filter(
                pk__in=queryset.values_list("user_id", flat=True)
            )
            .select_related("team")
            .values_list("team__name", "team")
            .order_by("team__name")
            .distinct()
        )
        return (
            dict(name=wf[0], id=wf[-1], work_for_type=team_type)
            for wf in work_infos
        )

    @staticmethod
    def resolve_expense_report_submitter_list(
        root, info, is_client, is_pending
    ):
        user = info.context.user

        queryset = get_approval_expense_report_queryset(
            queryset=ExpenseReport.objects.all(),
            current_user=user,
            is_client=is_client,
            is_pending=is_pending,
        )

        return User.objects.filter(
            pk__in=queryset.values_list("user_id", flat=True)
        )

    @staticmethod
    def resolve_expense_report_total_amount(
        root,
        info,
        is_my_expense,
        is_client,
        is_pending,
        payment_due,
        submitter,
        submitter_name,
        work_for,
        report_ids,
        has_finish,
    ):
        user = info.context.user
        reports = ExpenseReport.objects.all()

        if report_ids:
            reports = reports.filter(id__in=report_ids)
        else:
            if is_my_expense:
                reports = get_my_expense_report_queryset(
                    queryset=reports,
                    current_user=user,
                    is_pending=is_pending,
                    payment_due=payment_due,
                )
            else:
                reports = get_approval_expense_report_queryset(
                    queryset=reports,
                    current_user=user,
                    is_client=is_client,
                    is_pending=is_pending,
                    payment_due=payment_due,
                    submitter=submitter,
                    submitter_name=submitter_name,
                    work_for=work_for,
                    has_finish=has_finish,
                )

        result_map = {}
        result = []
        for report in reports:
            currency = report.payments_currency
            if not result_map.get(currency):
                result_map[currency] = 0
            amount = sum(exp.converted_amount for exp in report.expenses.all())
            result_map[currency] += amount

        for (k, v) in result_map.items():
            result.append(ReportAmountInfoNode(currency=k, amount="%.2f" % v))

        return result

    @staticmethod
    def resolve_expense_attachment_list(root, info, expense_id):
        return ExpenseAttachment.objects.filter(expense_id=expense_id)
