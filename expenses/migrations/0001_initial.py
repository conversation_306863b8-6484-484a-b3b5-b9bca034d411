# Generated by Django 3.1 on 2021-06-03 09:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("adm", "0003_auto_20210526_0729"),
    ]

    operations = [
        migrations.CreateModel(
            name="ExpensePolicy",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("has_delete", models.BooleanField(default=False)),
                ("name", models.CharField(max_length=50)),
                (
                    "kind",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "Ordered"), (2, "Unordered")],
                        db_index=True,
                        default=1,
                    ),
                ),
                (
                    "provider",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="policies",
                        to="adm.providerprofile",
                    ),
                ),
            ],
            options={
                "db_table": "expenses_policy",
            },
        ),
        migrations.CreateModel(
            name="ExpenseAction",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("has_delete", models.BooleanField(default=False)),
                ("name", models.CharField(max_length=50)),
                ("description", models.CharField(default="", max_length=100)),
                ("index", models.PositiveSmallIntegerField(default=0)),
                (
                    "action_type",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "Key Action"), (2, "Secondary Action")],
                        default=1,
                    ),
                ),
                (
                    "kind",
                    models.PositiveSmallIntegerField(
                        choices=[(1, "Common"), (2, "Archived")], default=1
                    ),
                ),
                (
                    "policy",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="actions",
                        to="expenses.expensepolicy",
                    ),
                ),
            ],
            options={
                "db_table": "expenses_action",
            },
        ),
    ]
