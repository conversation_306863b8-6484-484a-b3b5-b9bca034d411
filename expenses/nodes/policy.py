import graphene
from django.db import transaction
from graphene_django_extras import DjangoListObjectType, DjangoObjectType

from expenses.filters import ExpensePolicyFilter
from expenses.models import ExpenseAction, ExpensePolicy
from expenses.nodes.action import ExpenseActionNode
from expenses.utils.policy import (
    check_policy_can_delete,
    check_policy_can_update,
    do_delete_policy,
)
from user.models import User
from utils.constant import Permission
from utils.exceptionhandler import ErrMsg
from utils.permission import permissions_checker
from utils.utils import get_logger


class ExpensePolicyNode(DjangoObjectType):
    class Meta:
        model = ExpensePolicy
        exclude = ("has_delete",)

    id = graphene.Int()
    provider_id = graphene.Int()
    kind = graphene.Int()
    payment_cycle = graphene.Int()
    actions = graphene.List(ExpenseActionNode)
    step_num = graphene.Int()
    user_num = graphene.Int()
    can_update = graphene.Boolean(default_value=False)
    can_delete = graphene.Boolean(default_value=False)

    @staticmethod
    def resolve_actions(parent, info):
        return ExpenseAction.objects.filter(policy_id=parent.id).order_by(
            "index"
        )

    @staticmethod
    def resolve_step_num(parent, info):
        return ExpenseAction.objects.filter(policy_id=parent.id).count()

    @staticmethod
    def resolve_user_num(parent, info):
        return User.objects.filter(
            expense_process__policy_id=parent.id
        ).count()

    @staticmethod
    def resolve_can_update(parent, info):
        return check_policy_can_update(parent)

    @staticmethod
    def resolve_can_delete(parent, info):
        return check_policy_can_delete(parent)


class ExpensePolicyListNode(DjangoListObjectType):
    class Meta:
        model = ExpensePolicy
        exclude = ("has_delete",)
        filterset_class = ExpensePolicyFilter
        queryset = ExpensePolicy.objects.exclude(has_archived=True)


class SaveExpenseActionInput(graphene.InputObjectType):
    id = graphene.Int()
    name = graphene.String(required=True, description="max-length=50")
    description = graphene.String(description="max-length=100")
    index = graphene.Int(required=True, description="sequence number")
    action_type = graphene.Int(
        default_value=ExpenseAction.ACTION_KEY,
        description="""
        (1, 'Key Action'),
        (2, 'Secondary Action'),
    """,
    )
    kind = graphene.Int(
        default_value=ExpenseAction.KIND_COMMON,
        description="""
        (1, 'Common'),
        (2, 'Archived'),
    """,
    )


class AddExpensePolicyInput(graphene.InputObjectType):
    provider_id = graphene.Int(required=True)
    name = graphene.String(required=True, description="max-length=50")
    kind = graphene.Int(
        default_value=ExpensePolicy.KIND_ORDERED,
        description="""
        (1, "Ordered"),
        (2, "Unordered"),
    """,
    )
    payment_cycle = graphene.Int(
        required=True,
        description="""
        (1, 'Monthly'),
        (2, 'Every Two Weeks'),
    """,
    )
    actions = graphene.List(SaveExpenseActionInput, required=True)


class UpdateExpensePolicyInput(graphene.InputObjectType):
    provider_id = graphene.Int()
    name = graphene.String(description="max-length=50")
    kind = graphene.Int(
        description="""
        (1, "Ordered"),
        (2, "Unordered"),
    """
    )
    payment_cycle = graphene.Int(
        description="""
        (1, 'Monthly'),
        (2, 'Every Two Weeks'),
    """
    )
    actions = graphene.List(SaveExpenseActionInput)


class AddExpensePolicyMutation(graphene.Mutation):
    class Arguments:
        data = AddExpensePolicyInput(required=True)

    status = graphene.Boolean(description="request status")
    message = graphene.String(description="error message")
    expense_policy = graphene.Field(
        ExpensePolicyNode, description="policy data"
    )

    @classmethod
    @permissions_checker(permissions=[Permission.EXPENSE_POLICY_FULLACCESS])
    def mutate(cls, root, info, data: AddExpensePolicyInput):
        if ExpensePolicy.objects.filter(
            name=data.name, provider_id=data.provider_id, has_archived=False
        ).exists():
            return cls(status=False, message=ErrMsg.NAMEEXISTMSG)

        with transaction.atomic():
            policy = ExpensePolicy(
                name=data.name,
                provider_id=data.provider_id,
                kind=data.kind,
                payment_cycle=data.payment_cycle,
            )
            policy.save()

            get_logger("api").critical(
                "[EXPENSE] User(%d) create expense policy(%d)"
                % (info.context.user.id, policy.id)
            )

            ExpenseAction.objects.bulk_create(
                [
                    ExpenseAction(
                        **dict(
                            policy_id=policy.id,
                            name=a.name,
                            description=a.description or "",
                            index=a.index,
                            action_type=a.action_type,
                            kind=a.kind,
                        )
                    )
                    for a in data.actions
                ]
            )

        return cls(status=True, expense_policy=policy)


class UpdateExpensePolicyMutation(graphene.Mutation):
    class Arguments:
        id = graphene.ID(required=True)
        data = UpdateExpensePolicyInput(required=True)

    status = graphene.Boolean(description="request status")
    message = graphene.String(description="error message")
    expense_policy = graphene.Field(
        ExpensePolicyNode, description="policy data"
    )

    @classmethod
    @permissions_checker(permissions=[Permission.EXPENSE_POLICY_FULLACCESS])
    def mutate(cls, root, info, id: int, data: UpdateExpensePolicyInput):
        try:
            policy = ExpensePolicy.objects.get(pk=id)
            new_name = data.name or policy.name

            if data.provider_id and policy.provider_id != data.provider_id:
                if ExpensePolicy.objects.filter(
                    name=new_name,
                    provider_id=data.provider_id,
                    has_archived=False,
                ).exists():
                    return cls(status=False, message=ErrMsg.NAMEEXISTMSG)

            if data.name and policy.name != data.name:
                if ExpensePolicy.objects.filter(
                    name=data.name,
                    provider_id=policy.provider_id,
                    has_archived=False,
                ).exists():
                    return cls(status=False, message=ErrMsg.NAMEEXISTMSG)

        except policy.DoesNotExist:
            return cls(status=False, message="Record not found")

        # only update name
        if not check_policy_can_update(policy):
            policy.name = new_name
            policy.save()
            get_logger("api").critical(
                "[EXPENSE] User(%d) update expense policy(%d) name(%s)"
                % (info.context.user.id, policy.id, new_name)
            )
            return cls(status=True, expense_policy=policy)

        with transaction.atomic():
            policy.name = new_name
            policy.provider_id = data.provider_id or policy.provider_id
            policy.kind = data.kind or policy.kind
            policy.payment_cycle = data.payment_cycle or policy.payment_cycle
            policy.save()
            get_logger("api").critical(
                "[EXPENSE] User(%d) update expense policy(%d)"
                % (info.context.user.id, policy.id)
            )

            if data.actions:
                add_action_list = []
                current_actions = ExpenseAction.objects.filter(
                    policy_id=policy.id
                )
                old_action_id_list = list(
                    current_actions.values_list("id", flat=True)
                )

                for ac in data.actions:
                    # update
                    if ac.id:
                        current_ac = current_actions.get(pk=ac.id)
                        current_ac.name = ac.name or current_ac.name
                        current_ac.description = (
                            ac.description or current_ac.description
                        )
                        current_ac.index = ac.index
                        current_ac.action_type = (
                            ac.action_type or current_ac.action_type
                        )
                        current_ac.kind = ac.kind or current_ac.kind
                        current_ac.save()

                        old_action_id_list.remove(ac.id)
                        continue

                    # create
                    add_action_list.append(ac)

                # create
                if add_action_list:
                    for ac in add_action_list:
                        a = ExpenseAction(
                            policy_id=policy.id,
                            name=ac.name,
                            description=ac.description or "",
                            index=ac.index,
                            action_type=ac.action_type,
                            kind=ac.kind,
                        )
                        a.save()

                # delete
                if old_action_id_list:
                    ExpenseAction.objects.filter(
                        pk__in=old_action_id_list
                    ).update(has_delete=True)

        return cls(status=True, expense_policy=policy)


class DeleteExpensePolicyMutation(graphene.Mutation):
    class Arguments:
        id = graphene.ID(required=True)

    status = graphene.Boolean()
    message = graphene.String()

    @classmethod
    @permissions_checker(permissions=[Permission.EXPENSE_POLICY_FULLACCESS])
    def mutate(cls, _, info, id):
        with transaction.atomic():
            try:
                policy = ExpensePolicy.objects.get(pk=id)

                if not check_policy_can_delete(policy):
                    return cls(status=False, message="The policy is in use")

                do_delete_policy(policy, info.context.user)
            except ExpensePolicy.DoesNotExist:
                return cls(status=False, message=ErrMsg.NODATAMSG)
        return cls(status=True)
