# Generated by Django 3.1 on 2021-11-05 06:13

import django.db.models.deletion
import jsonfield.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("adm", "0013_auto_20210830_0536"),
    ]

    operations = [
        migrations.CreateModel(
            name="OnboardingTemplate",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("has_delete", models.BooleanField(default=False)),
                (
                    "work_permit_requirement",
                    models.PositiveSmallIntegerField(
                        choices=[(0, "No"), (1, "Tes")], default=0
                    ),
                ),
                ("has_published", models.BooleanField(default=False)),
                ("published_at", models.DateTimeField(null=True)),
                ("creator_id", models.PositiveIntegerField()),
                (
                    "country",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="onboarding_templates",
                        to="adm.area",
                    ),
                ),
                (
                    "provider",
                    models.ForeignKey(
                        db_index=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="onboarding_templates",
                        to="adm.providerprofile",
                    ),
                ),
            ],
            options={
                "index_together": {
                    ("provider", "country", "work_permit_requirement")
                },
            },
        ),
        migrations.CreateModel(
            name="OnboardingTemplateSection",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("has_delete", models.BooleanField(default=False)),
                (
                    "name",
                    models.CharField(
                        default="", db_index=True, max_length=100
                    ),
                ),
                ("description", models.CharField(default="", max_length=200)),
                ("is_basic", models.BooleanField(default=False)),
                (
                    "template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sections",
                        to="onboarding.onboardingtemplate",
                    ),
                ),
            ],
            options={
                "ordering": ["-id"],
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="OnboardingTemplateField",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("has_delete", models.BooleanField(default=False)),
                ("content", jsonfield.fields.JSONField(default="")),
                (
                    "section",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="fields",
                        to="onboarding.onboardingtemplatesection",
                    ),
                ),
            ],
            options={
                "ordering": ["-id"],
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="OnboardingTemplateBasicField",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("has_delete", models.BooleanField(default=False)),
                ("field_name", models.CharField(default="", max_length=50)),
                ("label_name", models.CharField(default="", max_length=100)),
                ("description", models.CharField(default="", max_length=200)),
                (
                    "section",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="basic_fields",
                        to="onboarding.onboardingtemplatesection",
                    ),
                ),
            ],
            options={
                "ordering": ["id"],
                "abstract": False,
            },
        ),
    ]
