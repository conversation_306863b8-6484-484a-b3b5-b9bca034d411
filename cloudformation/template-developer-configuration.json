{"Parameters": {"Name": "horizons-api", "Subnets": "subnet-0060f437bcdd5063c,subnet-0e18f2ace8744d2fa,subnet-03ad5005125560cfd", "VpcId": "vpc-05f31c79bb6d9d74f", "SecurityGroups": "sg-0622b4a082fb0174c,sg-0c2947374f11bff36", "Port": "80", "Count": "2", "Workers": "4", "Image": "${AWS_ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com/horizons-api:${CI_COMMIT_SHORT_SHA}", "ImageNginx": "${AWS_ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com/horizons-api-nginx:cf7b212", "Cpu": "2048", "Memory": "4096", "Region": "${REGION}", "LogGroup": "/horizons-services/${ENVIRONMENT_NAME}", "Cluster": "arn:aws:ecs:${REGION}:${AWS_ACCOUNT_ID}:cluster/horizons-services-${ENVIRONMENT_NAME}", "TaskRole": "arn:aws:iam::${AWS_ACCOUNT_ID}:role/horizons-api-${ENVIRONMENT_NAME}-${REGION}-ecs-task-role", "TaskExecutionRole": "arn:aws:iam::${AWS_ACCOUNT_ID}:role/horizons-api-${ENVIRONMENT_NAME}-${REGION}-ecs-task-execution-role", "Env": "${ENVIRONMENT_NAME}", "Stage": "${ENVIRONMENT_NAME}", "PartnersUIURL": "https://partners-${ENVIRONMENT_NAME}.joinhorizons.com", "OnboardingBccEmail": "<EMAIL>", "ContractorPayoutsReportEmailTo": "<EMAIL>", "ContractorPayoutsReportEmailCc": "<EMAIL>,djordje.r<PERSON><PERSON><PERSON><PERSON>@joinhorizons.com", "MaximumPercentApi": "200", "MinimumHealthyPercentApi": "100", "MaximumPercentCelery": "100", "MinimumHealthyPercentCelery": "0", "JoinUIURL": "https://${ENVIRONMENT_NAME}.joinhorizons.com", "JunoUIURL": "https://juno-${ENVIRONMENT_NAME}.joinhorizons.com", "ApiURL": "https://api-${ENVIRONMENT_NAME}.joinhorizons.com"}}