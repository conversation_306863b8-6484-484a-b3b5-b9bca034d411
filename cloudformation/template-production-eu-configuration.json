{"Parameters": {"Name": "horizons-api", "Subnets": "subnet-074e34a6b73f78d2b,subnet-04b83d7188023cb74,subnet-00d34e65076ab6b7f", "VpcId": "vpc-0807d0703b4176796", "Count": "4", "Workers": "16", "DocCount": "0", "MediaCount": "2", "SecurityGroups": "sg-0b6af6656e4ab7fca,sg-0d5c21470904c9c6d", "Port": "80", "Image": "${AWS_ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com/horizons-api:${CI_COMMIT_SHORT_SHA}", "ImageNginx": "${AWS_ACCOUNT_ID}.dkr.ecr.ap-southeast-1.amazonaws.com/horizons-api-nginx:cf7b212", "Cpu": "2048", "Memory": "8192", "Region": "${REGION}", "LogGroup": "/horizons-services/${ENVIRONMENT_NAME}", "Cluster": "arn:aws:ecs:${REGION}:${AWS_ACCOUNT_ID}:cluster/horizons-services-${ENVIRONMENT_NAME}", "TaskRole": "arn:aws:iam::${AWS_ACCOUNT_ID}:role/horizons-api-${ENVIRONMENT_NAME}-${REGION}-ecs-task-role", "TaskExecutionRole": "arn:aws:iam::${AWS_ACCOUNT_ID}:role/horizons-api-${ENVIRONMENT_NAME}-${REGION}-ecs-task-execution-role", "Env": "${ENVIRONMENT_NAME}", "Stage": "production", "AirwallexEnv": "prod", "OnboardingBccEmail": "<EMAIL>", "ContractorPayoutsReportEmailTo": "<EMAIL>", "ContractorPayoutsReportEmailCc": "<EMAIL>,<EMAIL>,<EMAIL>,djord<PERSON>.r<PERSON><PERSON><PERSON><PERSON>@joinhorizons.com,<EMAIL>", "CpuCelery": "1024", "MemoryCelery": "2048", "MaximumPercentApi": "200", "MinimumHealthyPercentApi": "100", "MaximumPercentCelery": "100", "MinimumHealthyPercentCelery": "0", "PartnersUIURL": "https://partners.joinhorizons.com", "KeyclockImage": "${AWS_ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com/keyclock:21.1.2", "JoinUIURL": "https://app.joinhorizons.com", "JunoUIURL": "https://juno.joinhorizons.com", "ApiURL": "https://api-${ENVIRONMENT_NAME}.joinhorizons.com"}}